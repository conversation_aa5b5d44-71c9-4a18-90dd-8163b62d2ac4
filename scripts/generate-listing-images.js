const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

const listings = [
  { name: 'yacht', width: 800, height: 600, color: '#1e40af' },
  { name: 'villa', width: 800, height: 600, color: '#166534' },
  { name: 'car', width: 800, height: 600, color: '#991b1b' },
  { name: 'luxury-suite', width: 800, height: 600, color: '#7e22ce' },
  { name: 'private-jet', width: 800, height: 600, color: '#0e7490' },
  { name: 'exotic-car', width: 800, height: 600, color: '#9d174d' }
];

const publicDir = path.join(__dirname, '../public/listings');

// Ensure the listings directory exists
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

// Generate image for each listing
listings.forEach(listing => {
  const canvas = createCanvas(listing.width, listing.height);
  const ctx = canvas.getContext('2d');
  
  // Fill background
  ctx.fillStyle = listing.color;
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // Add text
  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 40px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(
    listing.name.toUpperCase() + ' IMAGE', 
    canvas.width / 2, 
    canvas.height / 2
  );
  
  // Save as PNG
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(path.join(publicDir, `${listing.name}.jpg`), buffer);
});

console.log('Generated listing images in', publicDir);
