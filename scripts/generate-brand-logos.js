const fs = require('fs');
const path = require('path');

const brands = [
  { name: 'forbes', color: '#000000' },
  { name: 'bloomberg', color: '#000000' },
  { name: 'wsj', color: '#0074D9' },
  { name: 'time', color: '#E41B17' },
  { name: 'vogue', color: '#000000' }
];

const publicDir = path.join(__dirname, '../public/brands');

// Ensure the brands directory exists
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

// Generate SVG for each brand
brands.forEach(brand => {
  const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="40" viewBox="0 0 120 40" xmlns="http://www.w3.org/2000/svg">
  <rect width="120" height="40" rx="4" fill="#f3f4f6" />
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="14" fill="${brand.color}" text-anchor="middle" dominant-baseline="middle" font-weight="bold">${brand.name.toUpperCase()}</text>
</svg>`;

  fs.writeFileSync(path.join(publicDir, `${brand.name}.svg`), svg);
});

console.log('Generated brand logos in', publicDir);
