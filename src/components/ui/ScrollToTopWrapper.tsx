'use client';

import dynamic from 'next/dynamic';

// This is a client component wrapper for the ScrollToTop component
// that handles the dynamic import with SSR disabled
export default function ScrollToTopWrapper() {
  const ScrollToTop = dynamic(
    () => import('@/components/ui/scroll-to-top').then((mod) => mod.ScrollToTop),
    { 
      ssr: false,
      loading: () => null
    }
  );

  return <ScrollToTop />;
}
