'use client';

import Image from "next/image";
import { ArrowRight, Check, Star, Shield } from "lucide-react";
import { useEffect, useState } from 'react';

export default function HeroSection() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return (
      <section className="relative w-full min-h-screen overflow-hidden">
        <div className="w-full h-screen flex items-center justify-center">
          <div className="animate-pulse">
            <div className="w-32 h-32 mx-auto bg-muted rounded-full mb-6"></div>
            <div className="h-12 bg-muted rounded-full w-64 mx-auto mb-8"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="relative w-full overflow-hidden min-h-screen flex items-center">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-r from-teal-100/60 via-teal-50/20 to-amber-100/60 h-full" />
      </div>

      {/* Hero Content */}
      <div className="container mx-auto px-6 py-20 md:py-32 relative z-10">
        <div className="max-w-5xl mx-auto text-center">
          {/* Logo and Tagline */}
          <div className="flex flex-col items-center mb-12">
            <div className="w-24 h-24 md:w-32 md:h-32 relative mb-6">
              <Image
                src="/logo.png"
                alt="LXGO Concierge"
                width={128}
                height={128}
                className="object-contain w-full h-full"
                priority
              />
            </div>
            <span className="inline-block px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
              The Future of Luxury Lifestyle
            </span>
          </div>

          {/* Main Headline */}
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6">
            <span className="block">Elevate Your</span>
            <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Lifestyle Experience
            </span>
          </h1>

          {/* Subheadline */}
          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12">
            Discover curated luxury experiences, premium services, and exclusive access to the finest lifestyle offerings worldwide.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <button 
              type="button"
              className="px-8 py-4 rounded-xl font-semibold text-lg bg-gradient-to-r from-primary to-primary/90 text-white shadow-lg hover:shadow-xl transition-all hover:scale-[1.02] flex items-center justify-center gap-2"
            >
              Explore Services
              <ArrowRight className="w-5 h-5" />
            </button>
            <button className="px-8 py-4 rounded-xl font-semibold text-lg bg-background/80 backdrop-blur-sm border border-border/20 hover:border-primary/30 shadow-md transition-all hover:scale-[1.02] flex items-center gap-2">
              <span className="relative flex h-3 w-3">
                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary opacity-75"></span>
                <span className="relative inline-flex rounded-full h-3 w-3 bg-primary"></span>
              </span>
              Live Demo
            </button>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-16">
            {[
              { value: '50k+', label: 'Premium Members' },
              { value: '10k+', label: 'Luxury Partners' },
              { value: '24/7', label: 'Concierge Support' },
              { value: '100+', label: 'Destinations' },
            ].map((stat, index) => (
              <div key={index} className="bg-background/50 backdrop-blur-sm p-6 rounded-xl border border-border/20 hover:border-primary/30 transition-colors">
                <div className="text-3xl font-bold text-primary mb-2">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>

          {/* Trust Badges */}
          <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-emerald-500" />
              <span>Verified Luxury Partners</span>
            </div>
            <div className="h-4 w-px bg-border hidden sm:block" />
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4 text-amber-400 fill-current" />
              <span>5-Star Rated Service</span>
            </div>
            <div className="h-4 w-px bg-border hidden sm:block" />
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4 text-blue-500" />
              <span>Secure Booking</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
