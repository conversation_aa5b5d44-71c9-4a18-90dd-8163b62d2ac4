import Image from "next/image";
import { <PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Star, Gem } from "lucide-react";

const luxuryExperiences = [
  {
    id: 1,
    title: "Private Jet Charter",
    description: "Experience the pinnacle of air travel with our global private jet service",
    price: "From $15,000",
    rating: 5.0,
    features: ["Global Destinations", "24/7 Concierge", "Luxury Fleet"],
    image: "https://images.unsplash.com/photo-1559450314-5e465b1e3a3b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
  },
  {
    id: 2,
    title: "Luxury Yacht Charter",
    description: "Sail the world's most beautiful waters in ultimate privacy and comfort",
    price: "From $25,000/week",
    rating: 4.9,
    features: ["Fully Crewed", "Gourmet Dining", "Water Sports"],
    image: "https://images.unsplash.com/photo-1501785888041-af3ef285b470?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
  },
  {
    id: 3,
    title: "Private Island Getaway",
    description: "Your own paradise with white sand beaches and crystal clear waters",
    price: "From $10,000/night",
    rating: 5.0,
    features: ["Fully Staffed", "All Inclusive", "VIP Transfers"],
    image: "https://images.unsplash.com/photo-1552733407-5d5c46c0bb9b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
  }
];

export default function LuxurySection() {
  return (
    <section className="relative py-24 overflow-hidden bg-gradient-to-b from-background to-background/80">
      {/* Decorative elements */}
      <div className="absolute inset-0 -z-10 opacity-20">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDEzNSkiPjxyZWN0IHdpZHRoPSI1MCIgaGVpZ2h0PSI1IiBmaWxsPSJyZ2JhKDI1NSwyNTUsMjU1LDAuMDUpIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIi8+PC9zdmc+')]" />
      </div>
      
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto text-center mb-20">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-amber-900/20 text-amber-400 text-sm font-medium mb-6">
            <Sparkles className="w-4 h-4 mr-2" />
            LXGO Exclusives
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Unrivaled <span className="text-amber-400">Luxury Experiences</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Curated, exclusive experiences reserved for our most discerning members.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {luxuryExperiences.map((experience) => (
            <div 
              key={experience.id} 
              className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-amber-900/30 to-amber-800/10 border border-amber-500/20 hover:border-amber-400/40 transition-all hover:shadow-2xl hover:shadow-amber-500/10"
            >
              <div className="aspect-[4/3] relative overflow-hidden">
                <Image
                  src={experience.image}
                  alt={experience.title}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent" />
                <div className="absolute top-4 right-4 flex items-center bg-amber-500/90 text-white text-xs font-bold px-3 py-1 rounded-full">
                  <Star className="w-3 h-3 fill-current mr-1" />
                  {experience.rating}
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-2xl font-bold mb-2 text-amber-50">{experience.title}</h3>
                <p className="text-amber-100/80 mb-4">{experience.description}</p>
                
                <div className="space-y-3 mb-6">
                  {experience.features.map((feature, index) => (
                    <div key={index} className="flex items-center text-amber-100/80 text-sm">
                      <ShieldCheck className="w-4 h-4 mr-2 text-amber-400" />
                      {feature}
                    </div>
                  ))}
                </div>
                
                <div className="flex justify-between items-center pt-4 border-t border-amber-500/20">
                  <div className="text-amber-400 font-bold">{experience.price}</div>
                  <button className="inline-flex items-center px-4 py-2 bg-amber-600 hover:bg-amber-500 text-white rounded-lg font-medium transition-colors">
                    Enquire Now
                  </button>
                </div>
              </div>
              
              <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-amber-400/5 rounded-full transition-all duration-500 group-hover:scale-150" />
            </div>
          ))}
        </div>
        
        <div className="mt-16 text-center">
          <p className="text-amber-300/80 mb-6 flex items-center justify-center">
            <Gem className="w-5 h-5 mr-2" />
            Exclusive access for LXGO Black Card members
          </p>
          <button className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-amber-600 to-amber-500 text-white rounded-xl font-bold hover:shadow-lg hover:shadow-amber-500/20 transition-all">
            Become a Member <ArrowRight className="ml-2 w-5 h-5" />
          </button>
        </div>
      </div>
    </section>
  );
}
