import Image from "next/image";
import Link from "next/link";
import { Star, MapPin, Briefcase, ArrowRight } from "lucide-react";

const jobs = [
  {
    id: 1,
    title: "Executive Personal Chef",
    company: "Elite Culinary Services",
    location: "New York, NY",
    salary: "$120 - $250/hr",
    rating: 4.9,
    reviews: 128,
    type: "Full-time",
    image: "https://images.unsplash.com/photo-1606787366850-de6330128bfc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    skills: ["Gourmet Cuisine", "Meal Planning", "Food Safety"]
  },
  {
    id: 2,
    title: "Luxury Wellness Coach",
    company: "Tranquility Wellness",
    location: "Miami, FL",
    salary: "$150 - $300/hr",
    rating: 4.8,
    reviews: 94,
    type: "Contract",
    image: "https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1520&q=80",
    skills: ["Yoga", "Meditation", "Nutrition"]
  },
  {
    id: 3,
    title: "Elite Concierge Technician",
    company: "Premier Home Services",
    location: "Los Angeles, CA",
    salary: "$100 - $200/hr",
    rating: 4.7,
    reviews: 215,
    type: "Full-time",
    image: "https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    skills: ["Smart Home", "Appliance Repair", "Maintenance"]
  }
];

export default function JobsSection() {
  return (
    <section className="relative py-20 bg-background/5">
      <div className="absolute inset-0 -z-10 bg-gradient-to-b from-background/20 to-background/90" />
      
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Premium <span className="text-primary">Services</span> & Talent
          </h2>
          <p className="text-xl text-muted-foreground">
            Connect with vetted professionals offering exclusive services for the discerning client.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {jobs.map((job) => (
            <div key={job.id} className="group relative overflow-hidden rounded-2xl bg-card border border-border/20 hover:border-primary/30 transition-all hover:shadow-xl">
              <div className="aspect-[5/3] relative overflow-hidden">
                <Image
                  src={job.image}
                  alt={job.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
                <div className="absolute top-4 left-4 bg-background/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium">
                  {job.type}
                </div>
              </div>
              <div className="p-6">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="text-xl font-bold">{job.title}</h4>
                    <p className="text-muted-foreground">{job.company}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-primary">{job.salary}</div>
                    <div className="flex items-center text-sm text-amber-500">
                      <Star className="w-4 h-4 fill-current mr-1" />
                      {job.rating} ({job.reviews})
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center text-muted-foreground text-sm mb-4">
                  <MapPin className="w-4 h-4 mr-1" />
                  {job.location}
                </div>
                
                <div className="flex flex-wrap gap-2 mt-4">
                  {job.skills.map((skill, index) => (
                    <span key={index} className="text-xs px-3 py-1 bg-muted/50 rounded-full">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
              
              <div className="px-6 pb-6">
                <button className="w-full flex items-center justify-center px-4 py-2 border border-border rounded-lg font-medium hover:bg-muted/50 transition-colors">
                  View Details <ArrowRight className="ml-2 w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Link 
            href="/jobs"
            className="inline-flex items-center justify-center px-6 py-3 bg-primary text-white rounded-lg font-medium hover:bg-primary/90 transition-colors"
          >
            Browse All Services <Briefcase className="ml-2 w-4 h-4" />
          </Link>
        </div>
      </div>
    </section>
  );
}
