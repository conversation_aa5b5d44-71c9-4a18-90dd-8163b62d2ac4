import Image from "next/image";
import { Star, MapPin, ArrowRight, Calendar, Users } from "lucide-react";

const listings = [
  {
    id: 1,
    title: "Luxury Beachfront Villa",
    location: "Malibu, California",
    price: 1200,
    rating: 4.98,
    reviews: 124,
    image: "https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80",
    type: "Stays & Rentals",
    featured: true
  },
  {
    id: 2,
    title: "Sunset Yacht Charter",
    location: "Miami, Florida",
    price: 2500,
    rating: 4.9,
    reviews: 89,
    image: "https://images.unsplash.com/photo-1501785888041-af3ef285b470?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    type: "Boats & Yachts",
    featured: true
  },
  {
    id: 3,
    title: "Luxury Car Rental",
    location: "Los Angeles, California",
    price: 350,
    rating: 4.95,
    reviews: 210,
    image: "https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    type: "Vehicles",
    featured: true
  },
];

const categories = [
  { name: "Stays & Rentals", icon: "🏠", count: 1245 },
  { name: "Boats & Yachts", icon: "🛥️", count: 342 },
  { name: "Luxury Vehicles", icon: "🚗", count: 789 },
  { name: "Exclusive Events", icon: "🎭", count: 156 },
  { name: "Gourmet Dining", icon: "🍽️", count: 423 },
  { name: "Wellness & Spa", icon: "🧖", count: 267 },
];

export default function MarketplaceSection() {
  return (
    <section className="relative py-20 bg-background/50">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="max-w-3xl mx-auto text-center mb-16">
          <span className="inline-block px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
            Premium Marketplace
          </span>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Discover Extraordinary
            <span className="text-primary"> Experiences</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Handpicked luxury offerings from trusted partners worldwide
          </p>
        </div>

        {/* Featured Listings */}
        <div className="mb-20">
          <div className="flex items-center justify-between mb-8">
            <h3 className="text-2xl font-bold">Featured Listings</h3>
            <button className="flex items-center text-primary hover:underline">
              View all <ArrowRight className="ml-2 w-4 h-4" />
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {listings.map((item) => (
              <div key={item.id} className="group relative overflow-hidden rounded-2xl bg-card border border-border/20 hover:border-primary/30 transition-all hover:shadow-xl">
                <div className="aspect-video relative overflow-hidden">
                  <Image
                    src={item.image}
                    alt={item.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute top-4 right-4 bg-background/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium flex items-center">
                    <Star className="w-4 h-4 text-amber-400 fill-amber-400 mr-1" />
                    {item.rating} ({item.reviews})
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="text-xl font-bold line-clamp-1">{item.title}</h4>
                    <span className="text-sm text-muted-foreground">${item.price}<span className="text-xs">/day</span></span>
                  </div>
                  <div className="flex items-center text-muted-foreground text-sm mb-4">
                    <MapPin className="w-4 h-4 mr-1" />
                    {item.location}
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="px-3 py-1 bg-muted rounded-full text-xs">
                      {item.type}
                    </span>
                    <button className="text-primary hover:underline flex items-center">
                      View details <ArrowRight className="ml-1 w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Categories */}
        <div>
          <h3 className="text-2xl font-bold mb-8">Browse Categories</h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
            {categories.map((category, index) => (
              <div 
                key={index}
                className="group relative p-6 rounded-xl bg-card border border-border/20 hover:border-primary/30 transition-all hover:shadow-lg overflow-hidden"
              >
                <div className="text-3xl mb-3">{category.icon}</div>
                <h4 className="font-medium mb-1">{category.name}</h4>
                <p className="text-sm text-muted-foreground">{category.count} listings</p>
                <div className="absolute -bottom-4 -right-4 w-20 h-20 bg-primary/5 rounded-full transition-transform group-hover:scale-150" />
              </div>
            ))}
          </div>
        </div>

        {/* CTA Banner */}
        <div className="mt-20 bg-gradient-to-r from-primary/5 to-primary/10 rounded-2xl p-8 md:p-12 relative overflow-hidden">
          <div className="relative z-10 max-w-2xl">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">Ready to experience luxury?</h3>
            <p className="text-muted-foreground mb-6">Join thousands of members enjoying exclusive access to premium experiences worldwide.</p>
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="px-6 py-3 bg-primary text-white rounded-lg font-medium hover:bg-primary/90 transition-colors">
                Get Started
              </button>
              <button className="px-6 py-3 bg-background rounded-lg font-medium border border-border hover:bg-muted/50 transition-colors">
                Contact Sales
              </button>
            </div>
          </div>
          <div className="absolute -right-10 -bottom-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl" />
        </div>
      </div>
    </section>
  );
}
