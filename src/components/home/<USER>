import Image from "next/image";

export default function Header() {
  return (
    <header className="fixed top-0 left-0 w-full z-30 flex items-center justify-between px-8 py-4 glassmorphism shadow-xl backdrop-blur-lg bg-opacity-80 border-b border-border animate-fade-down">
      <div className="flex items-center gap-3">
        <Image src="/logo.png" alt="LXGO Logo" width={44} height={44} className="drop-shadow-xl" priority />
        <span className="text-2xl font-serif font-bold gradient-luxury bg-clip-text text-transparent tracking-tight animate-fade-in">LXGO Concierge</span>
      </div>
      <nav className="hidden md:flex gap-8 text-lg font-medium">
        <a href="#marketplace" className="hover:gradient-linear hover:text-transparent transition-all duration-200">Marketplace</a>
        <a href="#stays" className="hover:gradient-linear hover:text-transparent transition-all duration-200">Stays</a>
        <a href="#boats" className="hover:gradient-linear hover:text-transparent transition-all duration-200">Boats</a>
        <a href="#vehicles" className="hover:gradient-linear hover:text-transparent transition-all duration-200">Vehicles</a>
        <a href="#deals" className="hover:gradient-linear hover:text-transparent transition-all duration-200">Deals</a>
        <a href="#jobs" className="hover:gradient-linear hover:text-transparent transition-all duration-200">Jobs</a>
        <a href="#luxury" className="hover:gradient-linear hover:text-transparent transition-all duration-200">Luxury</a>
        <a href="#ai" className="hover:gradient-linear hover:text-transparent transition-all duration-200">Concierge AI</a>
      </nav>
      <div className="hidden md:flex items-center gap-3">
        <button className="px-6 py-2 rounded-full font-semibold bg-gradient-to-r from-[#FF8E63] via-[#FF7EB0] to-[#4B73FF] text-white shadow-lg hover:scale-105 transition-transform animate-pulse">Sign Up</button>
        <button className="px-6 py-2 rounded-full font-semibold glassmorphism-secondary text-foreground border border-border shadow-md hover:scale-105 transition-transform animate-glass-pop">Log In</button>
      </div>
      {/* Mobile menu button (not implemented) */}
      <div className="md:hidden">
        <button className="p-2 rounded-full glassmorphism-secondary border border-border">
          <span className="sr-only">Open menu</span>
          <svg width="28" height="28" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M4 6h16M4 12h16M4 18h16" /></svg>
        </button>
      </div>
    </header>
  );
}
