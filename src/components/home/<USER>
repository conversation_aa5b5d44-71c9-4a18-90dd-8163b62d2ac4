'use client';

import { Shield, Star, Globe, Zap, CheckCircle } from "lucide-react";
import Image from 'next/image';

const features = [
  {
    icon: <Shield className="w-6 h-6 text-primary" />,
    title: "Verified Luxury",
    description: "Every listing and partner is thoroughly vetted to ensure the highest standards of quality and authenticity."
  },
  {
    icon: <Star className="w-6 h-6 text-amber-400" />,
    title: "Exclusive Access",
    description: "Gain access to hidden gems and members-only experiences not available to the general public."
  },
  {
    icon: <Globe className="w-6 h-6 text-blue-500" />,
    title: "Global Network",
    description: "From New York to Tokyo, our curated selection spans the most sought-after destinations worldwide."
  },
  {
    icon: <Zap className="w-6 h-6 text-purple-500" />,
    title: "Instant Booking",
    description: "Secure your next luxury experience in just a few clicks with our streamlined booking process."
  },
  {
    icon: <CheckCircle className="w-6 h-6 text-emerald-500" />,
    title: "Hassle-Free Experience",
    description: "Dedicated concierge support ensures every detail of your experience is perfectly arranged."
  }
];

export default function ValuePropsSection() {
  return (
    <section className="relative py-20 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <span className="inline-block px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
            Why Choose Us
          </span>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            The <span className="text-primary">LXGO</span> Difference
          </h2>
          <p className="text-xl text-muted-foreground">
            We go beyond the ordinary to deliver exceptional experiences that create lasting memories.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="group relative p-8 bg-card rounded-2xl border border-border/20 hover:border-primary/30 transition-all hover:shadow-lg overflow-hidden"
            >
              <div className="w-16 h-16 rounded-xl bg-primary/5 flex items-center justify-center mb-6 group-hover:bg-primary/10 transition-colors">
                {feature.icon}
              </div>
              <h3 className="text-xl font-bold mb-3 text-foreground">{feature.title}</h3>
              <p className="text-muted-foreground">{feature.description}</p>
              <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-primary/5 rounded-full transition-transform duration-300 group-hover:scale-150" />
            </div>
          ))}
          
          {/* CTA Card */}
          <div className="relative p-8 rounded-2xl bg-gradient-to-br from-primary to-primary/80 text-white overflow-hidden group lg:col-span-3">
            <div className="relative z-10 text-center max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold mb-4">Ready to elevate your experience?</h3>
              <p className="text-lg text-white/90 mb-6 max-w-lg mx-auto">Join our community of discerning travelers and experience the world in style.</p>
              <button 
                type="button"
                className="px-6 py-3 bg-white text-primary rounded-lg font-medium hover:opacity-90 transition-all duration-300 transform hover:scale-[1.02]"
              >
                Get Started
              </button>
            </div>
            <div className="absolute -right-10 -bottom-10 w-40 h-40 bg-white/10 rounded-full blur-3xl transition-transform duration-500 group-hover:scale-125" />
          </div>
        </div>

        {/* Testimonial Section */}
        <div className="mt-20 bg-muted/30 rounded-2xl p-8 md:p-12 relative overflow-hidden">
          <div className="relative z-10 max-w-4xl mx-auto">
            <div className="flex items-center justify-center text-amber-400 mb-6">
              {[1, 2, 3, 4, 5].map((i) => (
                <Star key={i} className="w-6 h-6 fill-current mx-1" />
              ))}
            </div>
            <blockquote className="text-2xl md:text-3xl font-medium text-center mb-8">
              "LXGO transformed how we experience luxury travel. Their attention to detail and access to exclusive experiences is unmatched."
            </blockquote>
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 rounded-full bg-primary/10 mb-4 overflow-hidden">
                <Image 
                  src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" 
                  alt="Sarah J." 
                  width={64}
                  height={64}
                  className="w-full h-full object-cover"
                  priority
                />
              </div>
              <div className="text-center">
                <p className="font-bold">Sarah Johnson</p>
                <p className="text-muted-foreground">Loyal Member Since 2022</p>
              </div>
            </div>
          </div>
          <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-10" />
        </div>
      </div>
    </section>
  );
}
