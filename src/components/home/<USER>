import Image from "next/image";
import Link from "next/link";
import { MapP<PERSON>, ArrowRight, Zap, Users, Fuel } from "lucide-react";

const vehicles = [
  {
    id: 1,
    title: "Rolls Royce Ghost",
    location: "Los Angeles, CA",
    price: 899,
    type: "Luxury Sedan",
    year: 2023,
    seats: 5,
    transmission: "Automatic",
    image: "https://images.unsplash.com/photo-1616788494707-ec28f08b05bc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80"
  },
  {
    id: 2,
    title: "Lamborghini Huracán",
    location: "Miami, FL",
    price: 1499,
    type: "Supercar",
    year: 2023,
    seats: 2,
    transmission: "Automatic",
    image: "https://images.unsplash.com/photo-1580273916551-e352fca9d7d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80"
  },
  {
    id: 3,
    title: "Mercedes G-Wagon",
    location: "New York, NY",
    price: 799,
    type: "Luxury SUV",
    year: 2023,
    seats: 5,
    transmission: "Automatic",
    image: "https://images.unsplash.com/photo-1555215695-3004980ad54e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
  }
];

export default function VehiclesSection() {
  return (
    <section className="relative py-20 bg-background/10">
      <div className="absolute inset-0 -z-10 bg-gradient-to-b from-background/30 to-background/90" />
      <div className="absolute inset-0 -z-20 opacity-10" style={{
        backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29-22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%239C92AC\' fill-opacity=\'0.1\' fill-rule=\'evenodd\'/%3E%3C/svg%3E")',
      }} />
      
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto text-center mb-16">
          <span className="inline-block px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
            Luxury Fleet
          </span>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Premium <span className="text-primary">Vehicle Selection</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Experience the ultimate in luxury and performance with our curated collection of high-end vehicles.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {vehicles.map((vehicle) => (
            <div key={vehicle.id} className="group relative overflow-hidden rounded-2xl bg-card border border-border/20 hover:border-primary/30 transition-all hover:shadow-xl">
              <div className="aspect-[4/3] relative overflow-hidden">
                <Image
                  src={vehicle.image}
                  alt={vehicle.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute top-4 right-4 bg-background/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium">
                  ${vehicle.price}/day
                </div>
              </div>
              <div className="p-6">
                <div className="flex justify-between items-start mb-3">
                  <h4 className="text-xl font-bold">{vehicle.title}</h4>
                  <span className="text-sm text-muted-foreground">{vehicle.year}</span>
                </div>
                <div className="flex items-center text-muted-foreground text-sm mb-4">
                  <MapPin className="w-4 h-4 mr-1" />
                  {vehicle.location}
                </div>
                <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground">
                  <div className="flex flex-col items-center p-2 bg-muted/20 rounded-lg">
                    <Zap className="w-4 h-4 mb-1" />
                    {vehicle.transmission}
                  </div>
                  <div className="flex flex-col items-center p-2 bg-muted/20 rounded-lg">
                    <Users className="w-4 h-4 mb-1" />
                    {vehicle.seats} Seats
                  </div>
                  <div className="flex flex-col items-center p-2 bg-muted/20 rounded-lg">
                    <Fuel className="w-4 h-4 mb-1" />
                    {vehicle.type}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Link 
            href="/vehicles"
            className="inline-flex items-center justify-center px-6 py-3 bg-primary text-white rounded-lg font-medium hover:bg-primary/90 transition-colors"
          >
            View All Vehicles <ArrowRight className="ml-2 w-4 h-4" />
          </Link>
        </div>
      </div>
    </section>
  );
}
