'use client';

import Image from "next/image";
import { ArrowR<PERSON>, Check, Star, Shield } from "lucide-react";
import { useEffect, useState } from 'react';

export default function HeroSection() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return (
      <section className="relative w-full min-h-screen overflow-hidden">
        <div className="w-full h-screen flex items-center justify-center">
          <div className="animate-pulse">
            <div className="w-32 h-32 mx-auto bg-muted rounded-full mb-6"></div>
            <div className="h-12 bg-muted rounded-full w-64 mx-auto mb-8"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="relative w-full overflow-hidden min-h-screen flex items-center">
      {/* Background Gradient */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-r from-teal-50/60 via-teal-50/20 to-amber-50/60" />
      </div>

      {/* Hero Content */}
      <div className="container mx-auto px-6 py-20 md:py-32 relative z-10">
        <div className="max-w-5xl mx-auto text-center">
          {/* Logo and Tagline */}
          <div className="flex flex-col items-center mb-12">
            <div className="w-24 h-24 md:w-32 md:h-32 relative mb-6">
              <Image
                src="/logo.png"
                alt="LXGO Concierge"
                width={128}
                height={128}
                className="object-contain w-full h-full"
                priority
              />
            </div>
            <span className="inline-block px-6 py-3 rounded-full glass-teal text-primary text-sm font-medium mb-6 hover-glow">
              The Future of Luxury Lifestyle
            </span>
          </div>

          {/* Main Headline */}
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6 font-serif">
            <span className="block">Elevate Your</span>
            <span className="bg-gradient-to-r from-amber-600 to-amber-400 bg-clip-text text-transparent">
              Lifestyle Experience
            </span>
          </h1>

          {/* Subheadline */}
          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12">
            Discover curated luxury experiences, premium services, and exclusive access to the finest lifestyle offerings worldwide.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
            <button
              type="button"
              className="px-10 py-5 rounded-2xl font-semibold text-lg gradient-luxury text-white shadow-xl hover:shadow-2xl transition-all hover:scale-105 hover-lift flex items-center justify-center gap-3 animate-pulse-luxury"
            >
              Explore Services
              <ArrowRight className="w-5 h-5" />
            </button>
            <button className="px-10 py-5 rounded-2xl font-semibold text-lg glass border border-primary/30 hover:border-primary/50 shadow-lg transition-all hover:scale-105 hover-glow flex items-center gap-3">
              <span className="relative flex h-3 w-3">
                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-amber-400 opacity-75"></span>
                <span className="relative inline-flex rounded-full h-3 w-3 bg-amber-400"></span>
              </span>
              Live Demo
            </button>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-16">
            {[
              { value: '50k+', label: 'Premium Members' },
              { value: '10k+', label: 'Luxury Partners' },
              { value: '24/7', label: 'Concierge Support' },
              { value: '100+', label: 'Destinations' },
            ].map((stat, index) => (
              <div key={index} className="glass p-8 rounded-2xl border border-primary/20 hover:border-primary/40 transition-all hover-lift group">
                <div className="text-4xl font-bold text-luxury mb-3 group-hover:animate-glow">{stat.value}</div>
                <div className="text-sm text-muted-foreground font-medium">{stat.label}</div>
              </div>
            ))}
          </div>

          {/* Trust Badges */}
          <div className="flex flex-wrap items-center justify-center gap-8 text-sm">
            <div className="flex items-center gap-3 glass-teal px-4 py-2 rounded-full">
              <Check className="w-5 h-5 text-teal-500" />
              <span className="font-medium">Verified Luxury Partners</span>
            </div>
            <div className="flex items-center gap-3 glass-amber px-4 py-2 rounded-full">
              <Star className="w-5 h-5 text-amber-500 fill-current" />
              <span className="font-medium">5-Star Rated Service</span>
            </div>
            <div className="flex items-center gap-3 glass px-4 py-2 rounded-full">
              <Shield className="w-5 h-5 text-primary" />
              <span className="font-medium">Secure Booking</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
