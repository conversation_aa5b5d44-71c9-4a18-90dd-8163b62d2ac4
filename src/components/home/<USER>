import Image from "next/image";
import { Clock, Tag, ArrowRight } from "lucide-react";

const deals = [
  {
    id: 1,
    title: "Luxury Spa Retreat",
    description: "Full day spa experience with massage, facial, and hydrotherapy",
    originalPrice: 899,
    discountPrice: 599,
    discount: 33,
    image: "https://images.unsplash.com/photo-1545205597-3d9d02c29597?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    expires: "2025-12-31"
  },
  {
    id: 2,
    title: "Gourmet Dining Experience",
    description: "5-course tasting menu with wine pairing for two at a Michelin-starred restaurant",
    originalPrice: 1200,
    discountPrice: 799,
    discount: 34,
    image: "https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
    expires: "2025-11-30"
  },
  {
    id: 3,
    title: "Helicopter Tour",
    description: "Private 45-minute helicopter tour with champagne toast",
    originalPrice: 2500,
    discountPrice: 1799,
    discount: 28,
    image: "https://images.unsplash.com/photo-1501785888041-af3ef285b470?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    expires: "2025-10-15"
  }
];

const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('en-US', options);
};

export default function DealsSection() {
  return (
    <section className="relative py-20 bg-background/5">
      <div className="absolute inset-0 -z-10 bg-gradient-to-b from-background/20 to-background/80" />
      
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto text-center mb-16">
          <span className="inline-flex items-center px-4 py-2 rounded-full bg-amber-100 text-amber-800 text-sm font-medium mb-4">
            <Tag className="w-4 h-4 mr-2" />
            Limited Time Offers
          </span>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Exclusive <span className="text-primary">Member Deals</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Unlock special offers and limited-time experiences curated just for you.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {deals.map((deal) => (
            <div key={deal.id} className="group relative overflow-hidden rounded-2xl bg-card border border-border/20 hover:border-primary/30 transition-all hover:shadow-xl">
              <div className="aspect-[5/3] relative overflow-hidden">
                <Image
                  src={deal.image}
                  alt={deal.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
                <div className="absolute top-4 right-4 bg-amber-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                  {deal.discount}% OFF
                </div>
              </div>
              <div className="p-6">
                <div className="flex justify-between items-start mb-3">
                  <h4 className="text-xl font-bold">{deal.title}</h4>
                  <div className="text-right">
                    <span className="text-muted-foreground line-through text-sm">${deal.originalPrice}</span>
                    <div className="text-xl font-bold text-amber-500">${deal.discountPrice}</div>
                  </div>
                </div>
                <p className="text-muted-foreground text-sm mb-4">{deal.description}</p>
                <div className="flex items-center text-xs text-muted-foreground mt-4 pt-4 border-t border-border/20">
                  <Clock className="w-4 h-4 mr-1.5" />
                  <span>Expires {formatDate(deal.expires)}</span>
                </div>
              </div>
              <div className="absolute -bottom-10 right-4 w-20 h-20 bg-amber-500/10 rounded-full transition-all duration-300 group-hover:scale-150" />
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <button className="inline-flex items-center justify-center px-6 py-3 border border-border rounded-lg font-medium text-foreground hover:bg-muted/50 transition-colors">
            View All Deals <ArrowRight className="ml-2 w-4 h-4" />
          </button>
        </div>
      </div>
    </section>
  );
}
