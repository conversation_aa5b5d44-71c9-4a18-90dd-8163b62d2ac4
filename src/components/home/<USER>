'use client';

import { Shield, Star, Globe, Zap, CheckCircle } from "lucide-react";
import Image from 'next/image';

const features = [
  {
    icon: <Shield className="w-7 h-7 text-primary" />,
    title: "Verified Luxury",
    description: "Every listing and partner is thoroughly vetted to ensure the highest standards of quality and authenticity.",
    gradient: "from-teal-500/20 to-teal-600/20"
  },
  {
    icon: <Star className="w-7 h-7 text-amber-500" />,
    title: "Exclusive Access",
    description: "Gain access to hidden gems and members-only experiences not available to the general public.",
    gradient: "from-amber-500/20 to-amber-600/20"
  },
  {
    icon: <Globe className="w-7 h-7 text-primary" />,
    title: "Global Network",
    description: "From New York to Tokyo, our curated selection spans the most sought-after destinations worldwide.",
    gradient: "from-teal-500/20 to-amber-500/20"
  },
  {
    icon: <Zap className="w-7 h-7 text-amber-500" />,
    title: "Instant Booking",
    description: "Secure your next luxury experience in just a few clicks with our streamlined booking process.",
    gradient: "from-amber-500/20 to-teal-500/20"
  },
  {
    icon: <CheckCircle className="w-7 h-7 text-primary" />,
    title: "Hassle-Free Experience",
    description: "Dedicated concierge support ensures every detail of your experience is perfectly arranged.",
    gradient: "from-teal-500/20 to-teal-600/20"
  }
];

export default function ValuePropsSection() {
  return (
    <section className="relative py-20 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <span className="inline-block px-6 py-3 rounded-full glass-teal text-primary text-sm font-medium mb-6 hover-glow">
            Why Choose Us
          </span>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 font-serif">
            The <span className="text-luxury">LXGO</span> Difference
          </h2>
          <p className="text-xl text-muted-foreground">
            We go beyond the ordinary to deliver exceptional experiences that create lasting memories.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group relative p-8 glass rounded-2xl border border-primary/20 hover:border-primary/40 transition-all hover-lift overflow-hidden"
            >
              <div className={`w-20 h-20 rounded-2xl bg-gradient-to-br ${feature.gradient} flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg`}>
                {feature.icon}
              </div>
              <h3 className="text-xl font-bold mb-4 text-foreground font-serif">{feature.title}</h3>
              <p className="text-muted-foreground leading-relaxed">{feature.description}</p>
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-gradient-to-br from-primary/10 to-amber-500/10 rounded-full transition-transform duration-500 group-hover:scale-150 blur-xl" />
            </div>
          ))}
          
          {/* CTA Card */}
          <div className="relative p-12 rounded-3xl gradient-luxury text-white overflow-hidden group lg:col-span-3 shadow-2xl">
            <div className="relative z-10 text-center max-w-2xl mx-auto">
              <h3 className="text-3xl font-bold mb-6 font-serif">Ready to elevate your experience?</h3>
              <p className="text-lg text-white/90 mb-8 max-w-lg mx-auto leading-relaxed">Join our community of discerning travelers and experience the world in style.</p>
              <button
                type="button"
                className="px-8 py-4 bg-white text-primary rounded-2xl font-semibold hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Get Started
              </button>
            </div>
            <div className="absolute -right-12 -bottom-12 w-48 h-48 bg-white/10 rounded-full blur-3xl transition-transform duration-500 group-hover:scale-125 animate-float" />
            <div className="absolute -left-8 -top-8 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-float" style={{ animationDelay: '1s' }} />
          </div>
        </div>

        {/* Testimonial Section */}
        <div className="mt-20 glass rounded-3xl p-8 md:p-12 relative overflow-hidden border border-primary/20 hover:border-primary/30 transition-all">
          <div className="relative z-10 max-w-4xl mx-auto">
            <div className="flex items-center justify-center text-amber-500 mb-8">
              {[1, 2, 3, 4, 5].map((i) => (
                <Star key={i} className="w-7 h-7 fill-current mx-1 animate-pulse" style={{ animationDelay: `${i * 0.2}s` }} />
              ))}
            </div>
            <blockquote className="text-2xl md:text-3xl font-medium text-center mb-8 font-serif italic leading-relaxed">
              "              &ldquo;LXGO transformed how we experience luxury travel. Their attention to detail and access to exclusive experiences is unmatched.&rdquo;"
            </blockquote>
            <div className="flex flex-col items-center">
              <div className="w-20 h-20 rounded-full gradient-luxury p-1 mb-6 overflow-hidden shadow-xl">
                <div className="w-full h-full rounded-full overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
                    alt="Sarah J."
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                    priority
                  />
                </div>
              </div>
              <div className="text-center">
                <p className="font-bold text-lg">Sarah Johnson</p>
                <p className="text-muted-foreground">Loyal Member Since 2022</p>
              </div>
            </div>
          </div>
          <div className="absolute inset-0 bg-gradient-to-br from-teal-500/5 via-transparent to-amber-500/5" />
          <div className="absolute top-10 right-10 w-24 h-24 bg-gradient-radial from-amber-200/20 to-transparent rounded-full blur-xl animate-float" />
          <div className="absolute bottom-10 left-10 w-32 h-32 bg-gradient-radial from-teal-200/20 to-transparent rounded-full blur-xl animate-float" style={{ animationDelay: '1.5s' }} />
        </div>
      </div>
    </section>
  );
}
