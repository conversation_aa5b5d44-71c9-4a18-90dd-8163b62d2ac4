import { useState } from 'react';
import { Send, <PERSON><PERSON><PERSON>, Clock, CheckCircle, MessageCircle, Zap } from 'lucide-react';

type Message = {
  id: number;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
};

export default function ConciergeAISection() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      text: "Hello! I'm your LXGO Concierge AI. How can I assist you today? I can help you book experiences, find luxury services, or answer any questions you might have.",
      sender: 'ai',
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: messages.length + 1,
      text: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponses = [
        "I'd be happy to help with that. Let me find the best options for you.",
        "I'll check our exclusive collection for you right away.",
        "I have several excellent recommendations based on your request.",
        "Let me look up the most luxurious options available for you.",
      ];
      
      const aiMessage: Message = {
        id: messages.length + 2,
        text: aiResponses[Math.floor(Math.random() * aiResponses.length)],
        sender: 'ai',
        timestamp: new Date(),
      };
      
      setMessages((prev) => [...prev, aiMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const quickQuestions = [
    "Book me a luxury villa in Bali",
    "Find me a private chef for next weekend",
    "Recommend exclusive experiences in Paris",
    "I need a last-minute yacht charter"
  ];

  return (
    <section className="relative py-20 bg-gradient-to-b from-background to-background/80">
      <div className="absolute inset-0 -z-10 bg-grid-pattern opacity-5" />
      
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
            <Zap className="w-4 h-4 mr-2" />
            AI Concierge
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Your Personal <span className="text-primary">Luxury Concierge</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Available 24/7 to curate exceptional experiences just for you
          </p>
        </div>

        <div className="max-w-3xl mx-auto bg-card rounded-2xl shadow-xl overflow-hidden border border-border/20">
          {/* Chat header */}
          <div className="bg-gradient-to-r from-primary to-primary/80 p-6 text-white">
            <div className="flex items-center">
              <div className="relative">
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
              </div>
              <div className="ml-4">
                <h3 className="font-bold text-lg">LXGO Concierge</h3>
                <div className="flex items-center text-sm text-white/80">
                  <div className="w-2 h-2 rounded-full bg-green-400 mr-2"></div>
                  <span>Online</span>
                </div>
              </div>
            </div>
          </div>

          {/* Chat messages */}
          <div className="h-96 overflow-y-auto p-6 space-y-6">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-2xl px-4 py-3 ${
                    message.sender === 'user'
                      ? 'bg-primary text-white rounded-br-none'
                      : 'bg-muted/50 rounded-bl-none'
                  }`}
                >
                  <p>{message.text}</p>
                  <div className={`text-xs mt-1 flex items-center ${message.sender === 'user' ? 'text-white/70' : 'text-muted-foreground'}`}>
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    {message.sender === 'ai' && (
                      <CheckCircle className="w-3 h-3 ml-1 text-primary" />
                    )}
                  </div>
                </div>
              </div>
            ))}
            {isTyping && (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 rounded-full bg-primary/60 animate-bounce"></div>
                <div className="w-2 h-2 rounded-full bg-primary/60 animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 rounded-full bg-primary/60 animate-bounce" style={{ animationDelay: '0.4s' }}></div>
              </div>
            )}
          </div>

          {/* Quick questions */}
          <div className="px-6 pb-4">
            <div className="text-xs text-muted-foreground mb-2">Try asking:</div>
            <div className="flex flex-wrap gap-2">
              {quickQuestions.map((question, index) => (
                <button
                  key={index}
                  onClick={() => setInputValue(question)}
                  className="text-xs px-3 py-1.5 bg-muted/50 hover:bg-muted rounded-full transition-colors text-left"
                >
                  {question}
                </button>
              ))}
            </div>
          </div>

          {/* Input area */}
          <form onSubmit={handleSendMessage} className="border-t border-border/20 p-4">
            <div className="relative">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Message LXGO Concierge..."
                className="w-full bg-background border border-border rounded-full py-3 pl-5 pr-12 focus:outline-none focus:ring-2 focus:ring-primary/50"
              />
              <button
                type="submit"
                disabled={!inputValue.trim()}
                className="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-primary text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-primary/90 transition-colors"
              >
                <Send className="w-5 h-5" />
              </button>
            </div>
          </form>
        </div>

        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          <div className="bg-card p-6 rounded-xl border border-border/20">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-4">
              <Clock className="w-6 h-6" />
            </div>
            <h4 className="font-bold mb-2">24/7 Availability</h4>
            <p className="text-muted-foreground text-sm">Your personal concierge is always ready to assist, day or night.</p>
          </div>
          <div className="bg-card p-6 rounded-xl border border-border/20">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-4">
              <MessageCircle className="w-6 h-6" />
            </div>
            <h4 className="font-bold mb-2">Natural Conversations</h4>
            <p className="text-muted-foreground text-sm">Chat naturally and get human-like responses instantly.</p>
          </div>
          <div className="bg-card p-6 rounded-xl border border-border/20">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-4">
              <Zap className="w-6 h-6" />
            </div>
            <h4 className="font-bold mb-2">Instant Bookings</h4>
            <p className="text-muted-foreground text-sm">Reserve luxury experiences directly through the chat.</p>
          </div>
        </div>
      </div>
    </section>
  );
}
