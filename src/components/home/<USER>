import Image from "next/image";
import Link from "next/link";
import { <PERSON>, MapPin, ArrowRight } from "lucide-react";

const stays = [
  {
    id: 1,
    title: "Malibu Beach Villa",
    location: "Malibu, California",
    price: 1200,
    rating: 4.98,
    image: "https://images.unsplash.com/photo-1580587771525-78b9dba3b914?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
  },
  {
    id: 2,
    title: "NYC Penthouse",
    location: "New York, New York",
    price: 1800,
    rating: 4.9,
    image: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
  },
  {
    id: 3,
    title: "French Château",
    location: "Provence, France",
    price: 2500,
    rating: 4.99,
    image: "https://images.unsplash.com/photo-1493809842364-78817add7ffb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
  }
];

export default function StaysSection() {
  return (
    <section className="relative py-20 bg-background/50">
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto text-center mb-16">
          <span className="inline-block px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
            Luxury Stays
          </span>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Handpicked <span className="text-primary">Luxury Retreats</span>
          </h2>
          <p className="text-xl text-muted-foreground">
            Discover our curated collection of the world's most exquisite properties.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {stays.map((stay) => (
            <div key={stay.id} className="group relative overflow-hidden rounded-2xl bg-card border border-border/20 hover:border-primary/30 transition-all hover:shadow-xl">
              <div className="aspect-[4/3] relative overflow-hidden">
                <Image
                  src={stay.image}
                  alt={stay.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute top-4 right-4 bg-background/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium flex items-center">
                  <Star className="w-4 h-4 text-amber-400 fill-amber-400 mr-1" />
                  {stay.rating}
                </div>
              </div>
              <div className="p-6">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="text-xl font-bold">{stay.title}</h4>
                  <span className="text-muted-foreground">${stay.price}<span className="text-sm">/night</span></span>
                </div>
                <div className="flex items-center text-muted-foreground text-sm">
                  <MapPin className="w-4 h-4 mr-1" />
                  {stay.location}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Link 
            href="/stays"
            className="inline-flex items-center justify-center px-6 py-3 border border-border rounded-lg font-medium text-foreground hover:bg-muted/50 transition-colors"
          >
            View All Properties <ArrowRight className="ml-2 w-4 h-4" />
          </Link>
        </div>
      </div>
    </section>
  );
}
