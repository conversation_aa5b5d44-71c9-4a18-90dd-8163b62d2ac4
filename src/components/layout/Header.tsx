'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { Menu, X, ShoppingBag, User, Search, ChevronDown } from 'lucide-react';

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navLinks = [
    { name: 'Marketplace', href: '/marketplace' },
    { name: 'Stays', href: '/stays' },
    { name: 'Boats', href: '/boats' },
    { name: 'Vehicles', href: '/vehicles' },
    { name: 'Deals', href: '/deals' },
    { name: 'Jobs', href: '/jobs' },
    { name: 'Luxury', href: '/luxury' },
  ];

  return (
    <header 
      className={`fixed w-full z-50 transition-all duration-300 ${
        isScrolled 
          ? 'bg-background/80 backdrop-blur-md border-b border-border/10 shadow-sm' 
          : 'bg-transparent'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 md:h-20 items-center">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            <Link href="/" className="flex items-center">
              <Image
                src="/logo.png"
                alt="LXGO"
                width={200}
                height={200}
                className="h-10 w-auto"
                priority
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8 items-center">
            {navLinks.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-sm font-medium text-foreground/80 hover:text-primary transition-colors"
              >
                {item.name}
              </Link>
            ))}
            <div className="relative group">
              <button className="flex items-center text-sm font-medium text-foreground/80 hover:text-primary transition-colors">
                <span>More</span>
                <ChevronDown className="ml-1 h-4 w-4" />
              </button>
              <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-background/95 backdrop-blur-md border border-border/20 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                <div className="py-1">
                  <Link href="/about" className="block px-4 py-2 text-sm text-foreground/80 hover:bg-muted/50">About Us</Link>
                  <Link href="/contact" className="block px-4 py-2 text-sm text-foreground/80 hover:bg-muted/50">Contact</Link>
                  <Link href="/help" className="block px-4 py-2 text-sm text-foreground/80 hover:bg-muted/50">Help Center</Link>
                </div>
              </div>
            </div>
          </nav>

          {/* Right side buttons */}
          <div className="flex items-center space-x-4">
            <Link href="/search" className="p-2 text-foreground/80 hover:text-primary transition-colors">
              <Search className="h-5 w-5" />
            </Link>
            <Link href="/profile" className="p-2 text-foreground/80 hover:text-primary transition-colors">
              <User className="h-5 w-5" />
            </Link>
            <Link href="/cart" className="p-2 text-foreground/80 hover:text-primary transition-colors relative">
              <ShoppingBag className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span>
            </Link>
            <button 
              className="md:hidden p-2 text-foreground/80 hover:text-primary transition-colors"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-background/95 backdrop-blur-md border-t border-border/20">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navLinks.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="block px-3 py-2 text-base font-medium text-foreground/80 hover:bg-muted/50 rounded-md"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {item.name}
              </Link>
            ))}
            <div className="border-t border-border/20 mt-2 pt-2">
              <Link href="/about" className="block px-3 py-2 text-base font-medium text-foreground/80 hover:bg-muted/50 rounded-md">About Us</Link>
              <Link href="/contact" className="block px-3 py-2 text-base font-medium text-foreground/80 hover:bg-muted/50 rounded-md">Contact</Link>
              <Link href="/help" className="block px-3 py-2 text-base font-medium text-foreground/80 hover:bg-muted/50 rounded-md">Help Center</Link>
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
