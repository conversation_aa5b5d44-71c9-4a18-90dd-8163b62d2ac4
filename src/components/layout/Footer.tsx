import Link from 'next/link';
import Image from 'next/image';

export function Footer() {
  const currentYear = new Date().getFullYear();
  
  const footerLinks = [
    {
      title: 'Marketplace',
      links: [
        { name: 'All Listings', href: '/marketplace' },
        { name: 'Luxury Stays', href: '/stays' },
        { name: 'Yachts & Boats', href: '/boats' },
        { name: 'Premium Vehicles', href: '/vehicles' },
        { name: 'Exclusive Deals', href: '/deals' },
      ],
    },
    {
      title: 'Company',
      links: [
        { name: 'About Us', href: '/about' },
        { name: 'Careers', href: '/careers' },
        { name: 'Press', href: '/press' },
        { name: 'Blog', href: '/blog' },
        { name: 'Contact', href: '/contact' },
      ],
    },
    {
      title: 'Support',
      links: [
        { name: 'Help Center', href: '/help' },
        { name: 'Safety Information', href: '/safety' },
        { name: 'Cancellation Options', href: '/cancellation' },
        { name: 'Report an Issue', href: '/report' },
      ],
    },
    {
      title: 'Legal',
      links: [
        { name: 'Terms of Service', href: '/terms' },
        { name: 'Privacy Policy', href: '/privacy' },
        { name: 'Cookie Policy', href: '/cookies' },
        { name: 'Accessibility', href: '/accessibility' },
      ],
    },
  ];



  return (
    <footer className="bg-background border-t border-border/20">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 overflow-x-auto">
        <div className="flex flex-nowrap min-w-max gap-8">
          <div className="flex-shrink-0 w-80 pr-8">
            <div className="flex items-center space-x-2 mb-6">
              <div className="h-10 w-10 relative">
                <Image
                  src="/logo.png"
                  alt="LXGO"
                  width={40}
                  height={40}
                  className="h-full w-full object-contain"
                />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                LXGO
              </span>
            </div>
            <p className="text-muted-foreground text-sm mb-6">
              Elevating your lifestyle with curated luxury experiences and premium services worldwide.
            </p>
            <div className="text-sm text-muted-foreground">
              Follow us on social media for the latest updates
            </div>
          </div>
          
          {footerLinks.map((column, index) => (
            <div key={index} className="flex-shrink-0 w-48">
              <h3 className="text-sm font-semibold text-foreground mb-4">
                {column.title}
              </h3>
              <ul className="space-y-3">
                {column.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="text-sm text-muted-foreground hover:text-primary transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
        
        <div className="pt-8 border-t border-border/20 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground">
            © {currentYear} LXGO Concierge. All rights reserved.
          </p>
          <div className="flex items-center space-x-6 mt-4 md:mt-0">
            <Link href="/terms" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Terms of Service
            </Link>
            <Link href="/privacy" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Privacy Policy
            </Link>
            <Link href="/cookies" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Cookies
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
