'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Star, MapPin, Clock, Users } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';

interface SearchResult {
  id: string;
  title: string;
  location: string;
  price: number;
  rating: number;
  reviewCount: number;
  duration: string;
  capacity: string;
  image: string;
  category: string;
}

const mockResults: SearchResult[] = [
  {
    id: '1',
    title: 'Luxury Yacht Charter',
    location: 'Miami, FL',
    price: 3500,
    rating: 4.9,
    reviewCount: 124,
    duration: '8 hours',
    capacity: 'Up to 10 guests',
    image: 'https://images.unsplash.com/photo-1518540119877-991d5d3c5a0e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'experiences'
  },
  {
    id: '2',
    title: 'Beachfront Villa',
    location: 'Malibu, CA',
    price: 1200,
    rating: 4.8,
    reviewCount: 89,
    duration: '1 night',
    capacity: '6 guests, 3 bedrooms',
    image: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'stays'
  },
  {
    id: '3',
    title: 'Luxury Car Rental',
    location: 'Los Angeles, CA',
    price: 350,
    rating: 4.7,
    reviewCount: 215,
    duration: '24 hours',
    capacity: '2-4 passengers',
    image: 'https://images.unsplash.com/photo-1503376785-2a5b6c559a30?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'vehicles'
  },
  {
    id: '4',
    title: 'Private Sailing Tour',
    location: 'Key West, FL',
    price: 1200,
    rating: 4.9,
    reviewCount: 156,
    duration: '6 hours',
    capacity: 'Up to 8 guests',
    image: 'https://images.unsplash.com/photo-1501785888041-af3ef285b470?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'boats'
  },
  {
    id: '5',
    title: 'Mountain Cabin Retreat',
    location: 'Aspen, CO',
    price: 980,
    rating: 4.9,
    reviewCount: 203,
    duration: '1 night',
    capacity: '4 guests, 2 bedrooms',
    image: 'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'stays'
  },
  {
    id: '6',
    title: 'Private Helicopter Tour',
    location: 'New York, NY',
    price: 2500,
    rating: 5.0,
    reviewCount: 78,
    duration: '2 hours',
    capacity: 'Up to 3 guests',
    image: 'https://images.unsplash.com/photo-1500316124030-4cffa46f10f0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'experiences'
  },
];

interface SearchResultsProps {
  query: string;
}

export default function SearchResults({ query }: SearchResultsProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [results, setResults] = useState<SearchResult[]>([]);

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      if (!query) {
        setResults(mockResults);
      } else {
        const filtered = mockResults.filter(
          item =>
            item.title.toLowerCase().includes(query.toLowerCase()) ||
            item.location.toLowerCase().includes(query.toLowerCase()) ||
            item.category.toLowerCase().includes(query.toLowerCase())
        );
        setResults(filtered);
      }
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [query]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (results.length === 0) {
    return (
      <div className="text-center py-16">
        <h3 className="text-lg font-medium text-foreground mb-2">No results found</h3>
        <p className="text-muted-foreground mb-6">Try adjusting your search or filter criteria</p>
        <Button variant="outline">Clear all filters</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing <span className="font-medium text-foreground">{results.length}</span> results
          {query && (
            <>
              {' '}for <span className="font-medium text-foreground">&ldquo;{query}&rdquo;</span>
            </>
          )}
        </p>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">Sort by:</span>
          <select className="bg-background border border-input rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
            <option>Relevance</option>
            <option>Price: Low to High</option>
            <option>Price: High to Low</option>
            <option>Top Rated</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-6">
        {results.map((result, index) => (
          <motion.div
            key={result.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
            className="group"
          >
            <Link href={`/listing/${result.id}`}>
              <div className="bg-card rounded-xl overflow-hidden border border-border/20 hover:shadow-md transition-shadow">
                <div className="relative h-48 bg-muted/30">
                  <Image
                    src={result.image}
                    alt={result.title}
                    width={400}
                    height={300}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-sm font-medium flex items-center">
                    <Star className="w-3.5 h-3.5 text-amber-500 fill-amber-500 mr-1" />
                    {result.rating}
                    <span className="text-muted-foreground text-xs ml-1">({result.reviewCount})</span>
                  </div>
                </div>
                <div className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-lg leading-tight mb-1">{result.title}</h3>
                      <p className="text-sm text-muted-foreground flex items-center">
                        <MapPin className="h-3.5 w-3.5 mr-1" />
                        {result.location}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-semibold">${result.price}</p>
                      <p className="text-xs text-muted-foreground">total</p>
                    </div>
                  </div>
                  
                  <div className="mt-4 flex items-center text-xs text-muted-foreground space-x-4">
                    <span className="flex items-center">
                      <Clock className="h-3.5 w-3.5 mr-1" />
                      {result.duration}
                    </span>
                    <span className="flex items-center">
                      <Users className="h-3.5 w-3.5 mr-1" />
                      {result.capacity}
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
