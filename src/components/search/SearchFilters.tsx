'use client';

import { useState } from 'react';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Filter, X } from 'lucide-react';

type PriceRange = [number, number];

const categories = [
  { id: 'all', name: 'All Categories' },
  { id: 'stays', name: 'Stays' },
  { id: 'experiences', name: 'Experiences' },
  { id: 'vehicles', name: 'Vehicles' },
  { id: 'boats', name: 'Boats & Yachts' },
];

export default function SearchFilters() {
  const [priceRange, setPriceRange] = useState<PriceRange>([0, 10000]);
  const [selectedCategories, setSelectedCategories] = useState<Set<string>>(new Set(['all']));
  const [rating, setRating] = useState<number>(0);

  const handleCategoryChange = (categoryId: string) => {
    const newSelected = new Set(selectedCategories);
    if (categoryId === 'all') {
      newSelected.clear();
      newSelected.add('all');
    } else {
      newSelected.delete('all');
      if (newSelected.has(categoryId)) {
        newSelected.delete(categoryId);
      } else {
        newSelected.add(categoryId);
      }
      if (newSelected.size === 0) newSelected.add('all');
    }
    setSelectedCategories(newSelected);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="font-medium">Filters</h3>
        <Button variant="ghost" size="sm" className="text-sm text-muted-foreground">
          <X className="h-4 w-4 mr-1" />
          Clear all
        </Button>
      </div>

      <div className="space-y-4">
        <div>
          <h4 className="text-sm font-medium mb-3">Price range</h4>
          <div className="px-2">
            <Slider
              value={priceRange}
              onValueChange={(value) => setPriceRange(value as PriceRange)}
              min={0}
              max={10000}
              step={100}
              minStepsBetweenThumbs={1}
              className="mb-4"
            />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>${priceRange[0]}</span>
              <span>${priceRange[1]}+</span>
            </div>
          </div>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-3">Categories</h4>
          <div className="space-y-2">
            {categories.map((category) => (
              <div key={category.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`cat-${category.id}`}
                  checked={selectedCategories.has(category.id)}
                  onCheckedChange={() => handleCategoryChange(category.id)}
                />
                <label
                  htmlFor={`cat-${category.id}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {category.name}
                </label>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-3">Rating</h4>
          <div className="flex space-x-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                className={`p-1 ${star <= rating ? 'text-amber-400' : 'text-muted-foreground/30'}`}
                onClick={() => setRating(star === rating ? 0 : star)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    fillRule="evenodd"
                    d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            ))}
            {rating > 0 && (
              <button
                onClick={() => setRating(0)}
                className="ml-2 text-xs text-muted-foreground hover:text-foreground self-center"
              >
                Clear
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
