import { loadStripe } from '@stripe/stripe-js';
import Stripe from 'stripe';

// Client-side Stripe
export const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
);

// Server-side Stripe
export const stripe = new Stripe(
  process.env.STRIPE_SECRET_KEY!,
  {
    apiVersion: '2025-06-30.basil',
  }
);

// Stripe configuration
export const STRIPE_CONFIG = {
  currency: 'usd',
  payment_method_types: ['card'],
  mode: 'payment' as const,
};

// Service pricing tiers
export const PRICING_TIERS = {
  basic: {
    name: 'Basic Listing',
    price: 0,
    features: [
      'Standard listing visibility',
      'Basic support',
      'Standard search placement'
    ]
  },
  premium: {
    name: 'Premium Listing',
    price: 9900, // $99.00 in cents
    features: [
      'Featured placement',
      'Priority in search results',
      'Premium badge',
      'Enhanced analytics',
      'Priority support'
    ]
  },
  enterprise: {
    name: 'Enterprise',
    price: 29900, // $299.00 in cents
    features: [
      'All Premium features',
      'Dedicated account manager',
      'Custom branding',
      'API access',
      'White-label options'
    ]
  }
};

// Commission rates
export const COMMISSION_RATES = {
  basic: 0.15, // 15%
  premium: 0.10, // 10%
  enterprise: 0.05 // 5%
};
