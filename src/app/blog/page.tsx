'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { 
  Calendar, 
  Clock, 
  ArrowRight, 
  Tag,
  User,
  Search,
  Filter
} from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

const blogPosts = [
  {
    title: 'The Ultimate Guide to Luxury Travel in 2024',
    excerpt: 'Discover the most exclusive destinations and experiences that define luxury travel this year.',
    author: '<PERSON>',
    date: '2024-01-20',
    readTime: '8 min read',
    category: 'Travel',
    image: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    featured: true
  },
  {
    title: 'Michelin-Starred Dining: A Culinary Journey',
    excerpt: 'Explore the world\'s finest restaurants and the stories behind their exceptional cuisine.',
    author: '<PERSON>',
    date: '2024-01-15',
    readTime: '6 min read',
    category: 'Dining',
    image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  },
  {
    title: 'Private Aviation: The Future of Luxury Travel',
    excerpt: 'How private jets are revolutionizing the way we think about travel and time.',
    author: 'Isabella Rodriguez',
    date: '2024-01-10',
    readTime: '5 min read',
    category: 'Aviation',
    image: 'https://images.unsplash.com/photo-1559450314-5e465b1e3a3b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  },
  {
    title: 'Sustainable Luxury: The New Standard',
    excerpt: 'How the luxury industry is embracing sustainability without compromising on quality.',
    author: 'David Park',
    date: '2024-01-05',
    readTime: '7 min read',
    category: 'Lifestyle',
    image: 'https://images.unsplash.com/photo-1600298881974-6be191ceeda1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  },
  {
    title: 'Art and Culture: Exclusive Access Experiences',
    excerpt: 'Behind-the-scenes access to the world\'s most prestigious cultural institutions.',
    author: 'Emma Thompson',
    date: '2023-12-28',
    readTime: '9 min read',
    category: 'Culture',
    image: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  },
  {
    title: 'Wellness Retreats: Luxury Meets Mindfulness',
    excerpt: 'The rise of high-end wellness experiences that nurture both body and soul.',
    author: 'Michael Foster',
    date: '2023-12-20',
    readTime: '6 min read',
    category: 'Wellness',
    image: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  }
];

const categories = ['All', 'Travel', 'Dining', 'Aviation', 'Lifestyle', 'Culture', 'Wellness'];

export default function BlogPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-transparent to-secondary/5" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,oklch(0.55_0.18_180_/_0.08),transparent_50%)]" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="max-w-4xl mx-auto text-center"
            >
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-serif font-bold mb-6">
                <span className="block">Luxury</span>
                <span className="text-luxury">Insights</span>
              </h1>
              <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12">
                Discover the latest trends, insights, and stories from the world of luxury lifestyle.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Search and Filter */}
        <section className="py-10 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <div className="flex flex-col md:flex-row gap-4 mb-8">
                <div className="flex-1 relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="Search articles..."
                    className="w-full pl-12 pr-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Filter className="w-5 h-5 text-muted-foreground" />
                  <select className="px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors">
                    <option>All Categories</option>
                    {categories.slice(1).map(category => (
                      <option key={category}>{category}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Post */}
        <section className="py-10 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              {blogPosts.filter(post => post.featured).map((post, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                  className="glass rounded-3xl overflow-hidden border border-primary/20 hover:border-primary/30 transition-all hover-lift group mb-16"
                >
                  <div className="grid lg:grid-cols-2 gap-0">
                    <div className="aspect-[4/3] lg:aspect-auto relative overflow-hidden">
                      <Image
                        src={post.image}
                        alt={post.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="px-3 py-1 rounded-full text-xs font-medium glass-amber text-amber-600">
                          Featured
                        </span>
                      </div>
                    </div>
                    <div className="p-8 lg:p-12 flex flex-col justify-center">
                      <div className="flex items-center gap-4 mb-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <Tag className="w-4 h-4" />
                          <span>{post.category}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          <span>{new Date(post.date).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4" />
                          <span>{post.readTime}</span>
                        </div>
                      </div>
                      <h2 className="text-3xl font-serif font-bold mb-4">{post.title}</h2>
                      <p className="text-muted-foreground mb-6 leading-relaxed">{post.excerpt}</p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full gradient-luxury flex items-center justify-center text-white font-semibold">
                            {post.author.split(' ').map(n => n[0]).join('')}
                          </div>
                          <span className="font-medium">{post.author}</span>
                        </div>
                        <button className="px-6 py-3 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform flex items-center gap-2">
                          Read More
                          <ArrowRight className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Blog Grid */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {blogPosts.filter(post => !post.featured).map((post, index) => (
                  <motion.article
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="glass rounded-2xl overflow-hidden border border-border/20 hover:border-primary/30 transition-all hover-lift group"
                  >
                    <div className="aspect-[4/3] relative overflow-hidden">
                      <Image
                        src={post.image}
                        alt={post.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="px-3 py-1 rounded-full text-xs font-medium glass-teal text-primary">
                          {post.category}
                        </span>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex items-center gap-4 mb-3 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          <span>{new Date(post.date).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          <span>{post.readTime}</span>
                        </div>
                      </div>
                      <h3 className="text-xl font-serif font-bold mb-3 line-clamp-2">{post.title}</h3>
                      <p className="text-muted-foreground text-sm mb-4 line-clamp-3">{post.excerpt}</p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm font-medium">{post.author}</span>
                        </div>
                        <button className="text-primary hover:text-primary/80 transition-colors">
                          <ArrowRight className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </motion.article>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Newsletter Signup */}
        <section className="py-20 relative">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 gradient-luxury opacity-10" />
          </div>
          
          <div className="container mx-auto px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="max-w-3xl mx-auto"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Stay <span className="text-luxury">Informed</span>
              </h2>
              <p className="text-xl text-muted-foreground mb-8">
                Subscribe to our newsletter for the latest luxury insights and exclusive content.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-6 py-4 rounded-full glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                />
                <button className="px-8 py-4 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg">
                  Subscribe
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
