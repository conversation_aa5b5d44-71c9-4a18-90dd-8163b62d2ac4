'use client';

import { motion } from 'framer-motion';
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  Send,
  MessageCircle,
  Shield
} from 'lucide-react';
import { useState } from 'react';

const contactMethods = [
  {
    icon: Phone,
    title: '24/7 Concierge Hotline',
    description: 'Speak directly with our luxury concierge team',
    contact: '+1 (555) 123-LXGO',
    action: 'Call Now'
  },
  {
    icon: Mail,
    title: 'Email Support',
    description: 'Send us your detailed requirements',
    contact: '<EMAIL>',
    action: 'Send Email'
  },
  {
    icon: MessageCircle,
    title: 'Live Chat',
    description: 'Instant messaging with our team',
    contact: 'Available 24/7',
    action: 'Start Chat'
  }
];

const offices = [
  {
    city: 'New York',
    address: '432 Park Avenue, Suite 1200\nNew York, NY 10016',
    phone: '+****************',
    hours: 'Mon-Fri: 9AM-6PM EST'
  },
  {
    city: 'London',
    address: '1 Mayfair Place\nLondon W1J 8AJ, UK',
    phone: '+44 20 7123 4567',
    hours: 'Mon-Fri: 9AM-6PM GMT'
  },
  {
    city: 'Dubai',
    address: 'Burj Khalifa, Level 148\nDubai, UAE',
    phone: '+971 4 123 4567',
    hours: 'Sun-Thu: 9AM-6PM GST'
  }
];

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service: '',
    message: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <main className="pt-24 pb-20">
      {/* Hero Section */}
      <section className="pt-32 pb-20 relative overflow-hidden">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-transparent to-secondary/5" />
          <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-[0.02]" />
        </div>
        
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto text-center"
          >
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-serif font-bold mb-6">
              <span className="block">Get in</span>
              <span className="text-luxury">Touch</span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12">
              Our dedicated concierge team is available 24/7 to assist you with 
              any luxury service requirements.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-20 relative">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto mb-20">
            {contactMethods.map((method, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className="glass rounded-2xl p-8 border border-border/20 hover:border-primary/30 transition-all duration-500 hover:shadow-xl">
                  <div className="w-16 h-16 mx-auto mb-6 rounded-full gradient-luxury flex items-center justify-center group-hover:scale-110 transition-transform">
                    <method.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold mb-4">{method.title}</h3>
                  <p className="text-muted-foreground mb-4">{method.description}</p>
                  <p className="font-medium text-primary mb-6">{method.contact}</p>
                  <button className="px-6 py-2 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform">
                    {method.action}
                  </button>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Contact Form & Info */}
          <div className="grid lg:grid-cols-2 gap-16 max-w-6xl mx-auto">
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-serif font-bold mb-6">
                Send us a <span className="text-luxury">Message</span>
              </h2>
              <p className="text-muted-foreground mb-8">
                Tell us about your requirements and we&apos;ll get back to you within 2 hours.
              </p>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">Full Name</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                      placeholder="Your full name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Email Address</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">Phone Number</label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                      placeholder="+****************"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Service Interest</label>
                    <select
                      name="service"
                      value={formData.service}
                      onChange={handleChange}
                      className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                    >
                      <option value="">Select a service</option>
                      <option value="private-aviation">Private Aviation</option>
                      <option value="luxury-transportation">Luxury Transportation</option>
                      <option value="accommodations">Luxury Accommodations</option>
                      <option value="dining">Fine Dining</option>
                      <option value="events">Event Planning</option>
                      <option value="shopping">Personal Shopping</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Message</label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows={6}
                    className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors resize-none"
                    placeholder="Tell us about your requirements..."
                    required
                  />
                </div>

                <button
                  type="submit"
                  className="w-full px-8 py-4 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg flex items-center justify-center gap-2"
                >
                  Send Message
                  <Send className="w-5 h-5" />
                </button>
              </form>
            </motion.div>

            {/* Office Locations */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-serif font-bold mb-6">
                Our <span className="text-luxury">Offices</span>
              </h2>
              <p className="text-muted-foreground mb-8">
                Visit us at one of our luxury locations around the world.
              </p>

              <div className="space-y-8">
                {offices.map((office, index) => (
                  <div key={index} className="glass rounded-xl p-6 border border-border/20">
                    <h3 className="text-xl font-semibold mb-4 text-primary">{office.city}</h3>
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <MapPin className="w-5 h-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <p className="text-muted-foreground whitespace-pre-line">{office.address}</p>
                      </div>
                      <div className="flex items-center gap-3">
                        <Phone className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                        <p className="text-muted-foreground">{office.phone}</p>
                      </div>
                      <div className="flex items-center gap-3">
                        <Clock className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                        <p className="text-muted-foreground">{office.hours}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Additional Info */}
              <div className="mt-8 glass rounded-xl p-6 border border-border/20">
                <div className="flex items-center gap-3 mb-4">
                  <Shield className="w-6 h-6 text-primary" />
                  <h3 className="text-lg font-semibold">Privacy & Security</h3>
                </div>
                <p className="text-muted-foreground text-sm">
                  All communications are encrypted and confidential. We adhere to the highest 
                  standards of privacy protection for our discerning clientele.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 relative">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent" />
        </div>
        
        <div className="container mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
              Need <span className="text-luxury">Immediate</span> Assistance?
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              Our concierge team is standing by to assist you with any urgent requirements.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-8 py-4 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg flex items-center justify-center gap-2">
                <Phone className="w-5 h-5" />
                Call +1 (555) 123-LXGO
              </button>
              <button className="px-8 py-4 rounded-full glass border border-primary/20 text-foreground font-semibold hover:border-primary/40 transition-colors flex items-center justify-center gap-2">
                <MessageCircle className="w-5 h-5" />
                Start Live Chat
              </button>
            </div>
          </motion.div>
        </div>
      </section>
    </main>
  );
}
