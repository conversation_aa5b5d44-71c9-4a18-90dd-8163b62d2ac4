'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { useState } from 'react';
import {
  ArrowLeft,
  MapPin,
  DollarSign,
  Clock,
  Users,
  Star,
  Save,
  Eye,
  AlertCircle,
  Crown
} from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

interface ServiceFormData {
  title: string;
  category: string;
  description: string;
  price: string;
  duration: string;
  location: string;
  capacity: string;
  amenities: string[];
  images: string[];
  isPremium: boolean;
  status: 'draft' | 'pending' | 'active';
}

const categories = [
  'Stays', 'Boats', 'Aviation', 'Vehicles', 'Dining', 'Events', 'Experiences', 'Other'
];

const amenityOptions = [
  'WiFi', 'Air Conditioning', 'Pool', 'Spa', 'Gym', 'Restaurant', 'Bar', 
  'Concierge', 'Valet Parking', 'Butler Service', 'Private Chef', 'Helicopter Pad'
];

export default function CreateService() {
  const [formData, setFormData] = useState<ServiceFormData>({
    title: '',
    category: '',
    description: '',
    price: '',
    duration: '',
    location: '',
    capacity: '',
    amenities: [],
    images: [],
    isPremium: false,
    status: 'draft'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof ServiceFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleAmenityToggle = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.category) newErrors.category = 'Category is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.price.trim()) newErrors.price = 'Price is required';
    if (!formData.location.trim()) newErrors.location = 'Location is required';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (status: 'draft' | 'pending') => {
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    try {
      // Here you would submit to your API
      const serviceData = { ...formData, status };
      console.log('Submitting service:', serviceData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Redirect to services list
      window.location.href = '/dashboard/services';
    } catch (error) {
      console.error('Error creating service:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-20">
        <div className="container mx-auto px-6 py-8 max-w-4xl">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Link
              href="/dashboard/services"
              className="p-2 rounded-full glass border border-border/20 hover:border-primary/40 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <div>
              <h1 className="text-3xl font-serif font-bold">Create New Service</h1>
              <p className="text-muted-foreground">Add a new luxury service to your listings</p>
            </div>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Form */}
            <div className="lg:col-span-2 space-y-8">
              {/* Basic Information */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="glass rounded-2xl p-8 border border-border/20"
              >
                <h2 className="text-xl font-serif font-bold mb-6">Basic Information</h2>
                
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">Service Title *</label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      className={`w-full px-4 py-3 rounded-xl glass border ${errors.title ? 'border-red-500' : 'border-border/20'} focus:border-primary/40 focus:outline-none transition-colors`}
                      placeholder="e.g., Luxury Villa in Tuscany"
                    />
                    {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2">Category *</label>
                      <select
                        value={formData.category}
                        onChange={(e) => handleInputChange('category', e.target.value)}
                        className={`w-full px-4 py-3 rounded-xl glass border ${errors.category ? 'border-red-500' : 'border-border/20'} focus:border-primary/40 focus:outline-none transition-colors`}
                      >
                        <option value="">Select category</option>
                        {categories.map(cat => (
                          <option key={cat} value={cat}>{cat}</option>
                        ))}
                      </select>
                      {errors.category && <p className="text-red-500 text-sm mt-1">{errors.category}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">Location *</label>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                        <input
                          type="text"
                          value={formData.location}
                          onChange={(e) => handleInputChange('location', e.target.value)}
                          className={`w-full pl-10 pr-4 py-3 rounded-xl glass border ${errors.location ? 'border-red-500' : 'border-border/20'} focus:border-primary/40 focus:outline-none transition-colors`}
                          placeholder="e.g., Miami, FL"
                        />
                      </div>
                      {errors.location && <p className="text-red-500 text-sm mt-1">{errors.location}</p>}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Description *</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      rows={4}
                      className={`w-full px-4 py-3 rounded-xl glass border ${errors.description ? 'border-red-500' : 'border-border/20'} focus:border-primary/40 focus:outline-none transition-colors resize-none`}
                      placeholder="Describe your luxury service in detail..."
                    />
                    {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
                  </div>
                </div>
              </motion.div>

              {/* Pricing & Details */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="glass rounded-2xl p-8 border border-border/20"
              >
                <h2 className="text-xl font-serif font-bold mb-6">Pricing & Details</h2>
                
                <div className="grid md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">Price *</label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                      <input
                        type="number"
                        value={formData.price}
                        onChange={(e) => handleInputChange('price', e.target.value)}
                        className={`w-full pl-10 pr-4 py-3 rounded-xl glass border ${errors.price ? 'border-red-500' : 'border-border/20'} focus:border-primary/40 focus:outline-none transition-colors`}
                        placeholder="0"
                      />
                    </div>
                    {errors.price && <p className="text-red-500 text-sm mt-1">{errors.price}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Duration</label>
                    <div className="relative">
                      <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                      <input
                        type="text"
                        value={formData.duration}
                        onChange={(e) => handleInputChange('duration', e.target.value)}
                        className="w-full pl-10 pr-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                        placeholder="e.g., 3 days"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Capacity</label>
                    <div className="relative">
                      <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                      <input
                        type="number"
                        value={formData.capacity}
                        onChange={(e) => handleInputChange('capacity', e.target.value)}
                        className="w-full pl-10 pr-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                        placeholder="0"
                      />
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Amenities */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="glass rounded-2xl p-8 border border-border/20"
              >
                <h2 className="text-xl font-serif font-bold mb-6">Amenities</h2>
                
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {amenityOptions.map(amenity => (
                    <label key={amenity} className="flex items-center gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.amenities.includes(amenity)}
                        onChange={() => handleAmenityToggle(amenity)}
                        className="w-4 h-4 rounded border-border/20 text-primary focus:ring-primary/20"
                      />
                      <span className="text-sm">{amenity}</span>
                    </label>
                  ))}
                </div>
              </motion.div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Premium Upgrade */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="glass rounded-2xl p-6 border border-amber-500/20 bg-amber-50/10"
              >
                <div className="flex items-center gap-3 mb-4">
                  <Crown className="w-6 h-6 text-amber-500" />
                  <h3 className="font-serif font-bold">Premium Listing</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-4">
                  Boost your visibility with premium features:
                </p>
                <ul className="text-sm space-y-2 mb-6">
                  <li className="flex items-center gap-2">
                    <Star className="w-4 h-4 text-amber-500" />
                    <span>Featured placement</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <Star className="w-4 h-4 text-amber-500" />
                    <span>Priority in search</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <Star className="w-4 h-4 text-amber-500" />
                    <span>Premium badge</span>
                  </li>
                </ul>
                <label className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.isPremium}
                    onChange={(e) => handleInputChange('isPremium', e.target.checked)}
                    className="w-4 h-4 rounded border-border/20 text-amber-500 focus:ring-amber-500/20"
                  />
                  <span className="text-sm font-medium">Upgrade to Premium (+$99/month)</span>
                </label>
              </motion.div>

              {/* Actions */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
                className="glass rounded-2xl p-6 border border-border/20"
              >
                <h3 className="font-serif font-bold mb-4">Publish Options</h3>
                <div className="space-y-3">
                  <button
                    onClick={() => handleSubmit('draft')}
                    disabled={isSubmitting}
                    className="w-full px-4 py-3 rounded-full glass border border-border/20 text-foreground font-medium hover:border-primary/40 transition-colors flex items-center justify-center gap-2"
                  >
                    <Save className="w-4 h-4" />
                    Save as Draft
                  </button>
                  <button
                    onClick={() => handleSubmit('pending')}
                    disabled={isSubmitting}
                    className="w-full px-4 py-3 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform flex items-center justify-center gap-2"
                  >
                    <Eye className="w-4 h-4" />
                    Submit for Review
                  </button>
                </div>
                
                <div className="mt-4 p-3 rounded-xl bg-blue-50/50 border border-blue-200/20">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5" />
                    <p className="text-xs text-blue-700">
                      Services are reviewed within 24 hours before going live.
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
