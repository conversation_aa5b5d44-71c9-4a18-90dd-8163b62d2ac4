'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import {
  Plus,
  Edit,
  Eye,
  Star,
  MapPin,
  DollarSign,
  Clock,
  Users,
  Filter,
  Search,
  MoreVertical
} from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

interface Service {
  id: string;
  title: string;
  category: string;
  price: number;
  duration: string;
  location: string;
  status: 'active' | 'pending' | 'draft';
  rating: number;
  bookings: number;
  image: string;
  createdAt: string;
  isPremium: boolean;
}

const mockServices: Service[] = [
  {
    id: '1',
    title: 'Private Yacht Charter - Miami',
    category: 'Boats',
    price: 2500,
    duration: '8 hours',
    location: 'Miami, FL',
    status: 'active',
    rating: 4.9,
    bookings: 23,
    image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400',
    createdAt: '2024-01-15',
    isPremium: true
  },
  {
    id: '2',
    title: 'Luxury Villa - Tuscany',
    category: 'Stays',
    price: 800,
    duration: 'per night',
    location: 'Tuscany, Italy',
    status: 'active',
    rating: 4.8,
    bookings: 45,
    image: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400',
    createdAt: '2024-01-10',
    isPremium: false
  },
  {
    id: '3',
    title: 'Private Jet to Paris',
    category: 'Aviation',
    price: 15000,
    duration: '6 hours',
    location: 'New York, NY',
    status: 'pending',
    rating: 0,
    bookings: 0,
    image: 'https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=400',
    createdAt: '2024-01-20',
    isPremium: true
  }
];

export default function ServicesManagement() {
  const [services] = useState<Service[]>(mockServices);
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredServices = services.filter(service => {
    const matchesFilter = filter === 'all' || service.status === filter;
    const matchesSearch = service.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.category.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-20">
        <div className="container mx-auto px-6 py-8">
          {/* Header */}
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-serif font-bold mb-2">My Services</h1>
              <p className="text-muted-foreground">Manage your luxury service listings</p>
            </div>
            <Link
              href="/dashboard/services/create"
              className="mt-4 md:mt-0 px-6 py-3 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform flex items-center gap-2 w-fit"
            >
              <Plus className="w-5 h-5" />
              Add New Service
            </Link>
          </div>

          {/* Filters and Search */}
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search services..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="w-5 h-5 text-muted-foreground" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="draft">Draft</option>
              </select>
            </div>
          </div>

          {/* Services Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredServices.map((service, index) => (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="glass rounded-2xl overflow-hidden border border-border/20 hover:border-primary/30 transition-all hover-lift group"
              >
                <div className="aspect-[4/3] relative overflow-hidden">
                  <Image
                    src={service.image}
                    alt={service.title}
                    width={400}
                    height={300}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute top-3 left-3 flex gap-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                      {service.status}
                    </span>
                    {service.isPremium && (
                      <span className="px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                        Premium
                      </span>
                    )}
                  </div>
                  <div className="absolute top-3 right-3">
                    <button className="p-2 rounded-full bg-background/80 backdrop-blur-sm hover:bg-background transition-colors">
                      <MoreVertical className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <div className="p-6">
                  <h3 className="text-lg font-serif font-bold mb-2 line-clamp-2">{service.title}</h3>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                    <div className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      <span>{service.location}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>{service.duration}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-1">
                      <DollarSign className="w-4 h-4 text-green-600" />
                      <span className="font-semibold text-green-600">${service.price}</span>
                    </div>
                    {service.rating > 0 && (
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-amber-500 fill-current" />
                        <span className="text-sm font-medium">{service.rating}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-muted-foreground mb-6">
                    <div className="flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      <span>{service.bookings} bookings</span>
                    </div>
                    <span>Created {service.createdAt}</span>
                  </div>
                  
                  <div className="flex gap-2">
                    <Link
                      href={`/dashboard/services/${service.id}`}
                      className="flex-1 px-4 py-2 rounded-full glass border border-primary/20 text-primary font-medium hover:border-primary/40 transition-colors text-center flex items-center justify-center gap-2"
                    >
                      <Eye className="w-4 h-4" />
                      View
                    </Link>
                    <Link
                      href={`/dashboard/services/${service.id}/edit`}
                      className="flex-1 px-4 py-2 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform text-center flex items-center justify-center gap-2"
                    >
                      <Edit className="w-4 h-4" />
                      Edit
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {filteredServices.length === 0 && (
            <div className="text-center py-12">
              <div className="w-24 h-24 mx-auto mb-6 rounded-full glass border border-border/20 flex items-center justify-center">
                <Plus className="w-12 h-12 text-muted-foreground" />
              </div>
              <h3 className="text-xl font-serif font-bold mb-2">No services found</h3>
              <p className="text-muted-foreground mb-6">
                {searchTerm || filter !== 'all' 
                  ? 'Try adjusting your search or filters' 
                  : 'Start by creating your first luxury service listing'
                }
              </p>
              {!searchTerm && filter === 'all' && (
                <Link
                  href="/dashboard/services/create"
                  className="px-6 py-3 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform inline-flex items-center gap-2"
                >
                  <Plus className="w-5 h-5" />
                  Create Your First Service
                </Link>
              )}
            </div>
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
}
