'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Eye,
  Star,
  MapPin,
  DollarSign,
  Users,
  Calendar,
  TrendingUp,
  MessageSquare,
  Share2
} from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

interface ServiceDetailProps {
  params: {
    id: string;
  };
}

// Mock service data - in real app, fetch from API
const mockService = {
  id: '1',
  title: 'Luxury Yacht Charter',
  description: 'Experience the ultimate luxury with our private yacht charter. Perfect for special occasions, corporate events, or a lavish getaway.',
  price: 3500,
  rating: 4.9,
  reviewCount: 124,
  location: 'Miami, FL',
  duration: '8 hours',
  capacity: 'Up to 10 guests',
  category: 'Experiences',
  images: [
    'https://images.unsplash.com/photo-1518540119877-991d5d3c5a0e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1503177847378-d2048487fa46?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1505881502353-a1986add38fe?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
  ],
  amenities: [
    'Private crew',
    'Gourmet catering',
    'Water sports equipment',
    'Premium sound system',
    'Luxury seating areas',
    'Onboard jacuzzi'
  ],
  recentBookings: [
    { id: '1', guest: 'John Smith', date: '2024-02-15', amount: 3500, status: 'completed' },
    { id: '2', guest: 'Sarah Johnson', date: '2024-02-10', amount: 3500, status: 'completed' }
  ],
  reviews: [
    { id: '1', guest: 'John Smith', rating: 5, comment: 'Absolutely incredible experience! The yacht was pristine and the crew was exceptional.', date: '2024-02-16' },
    { id: '2', guest: 'Sarah Johnson', rating: 5, comment: 'Perfect for our anniversary celebration. Highly recommend!', date: '2024-02-11' }
  ],
  totalBookings: 25,
  totalRevenue: 87500,
  status: 'active',
  isPremium: true,
  createdAt: '2024-01-15'
};

export default function ServiceDetail({ }: ServiceDetailProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const service = mockService; // In real app, fetch based on params.id

  const stats = [
    { label: 'Total Bookings', value: service.totalBookings, icon: Calendar },
    { label: 'Total Revenue', value: `$${service.totalRevenue.toLocaleString()}`, icon: DollarSign },
    { label: 'Average Rating', value: service.rating, icon: Star },
    { label: 'Views This Month', value: '1,234', icon: Eye }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-20">
        <div className="container mx-auto px-6 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <Link
                href="/dashboard/services"
                className="p-2 rounded-full glass border border-border/20 hover:border-primary/40 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <div>
                <h1 className="text-3xl font-serif font-bold">{service.title}</h1>
                <div className="flex items-center gap-4 mt-2">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(service.status)}`}>
                    {service.status}
                  </span>
                  {service.isPremium && (
                    <span className="px-3 py-1 rounded-full text-sm font-medium bg-amber-100 text-amber-800">
                      Premium
                    </span>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex gap-3">
              <button className="px-4 py-2 rounded-full glass border border-border/20 text-foreground font-medium hover:border-primary/40 transition-colors flex items-center gap-2">
                <Share2 className="w-4 h-4" />
                Share
              </button>
              <Link
                href={`/dashboard/services/${service.id}/edit`}
                className="px-4 py-2 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform flex items-center gap-2"
              >
                <Edit className="w-4 h-4" />
                Edit
              </Link>
            </div>
          </div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8"
          >
            {stats.map((stat, index) => (
              <div key={index} className="glass rounded-xl p-6 border border-border/20 text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full gradient-luxury flex items-center justify-center">
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
                <div className="text-2xl font-bold text-luxury mb-1">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </motion.div>

          {/* Tabs */}
          <div className="mb-8">
            <div className="flex gap-1 p-1 glass rounded-xl border border-border/20 w-fit">
              {['overview', 'bookings', 'reviews', 'analytics'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`px-6 py-2 rounded-lg font-medium transition-all ${
                    activeTab === tab
                      ? 'gradient-luxury text-white'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  {tab.charAt(0).toUpperCase() + tab.slice(1)}
                </button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              {activeTab === 'overview' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-8"
                >
                  {/* Images */}
                  <div className="glass rounded-2xl p-8 border border-border/20">
                    <h2 className="text-xl font-serif font-bold mb-6">Photos</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {service.images.map((image, index) => (
                        <div key={index} className="aspect-[4/3] relative overflow-hidden rounded-xl">
                          <Image
                            src={image}
                            alt={`${service.title} - Image ${index + 1}`}
                            fill
                            className="object-cover hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Description */}
                  <div className="glass rounded-2xl p-8 border border-border/20">
                    <h2 className="text-xl font-serif font-bold mb-6">Description</h2>
                    <p className="text-muted-foreground leading-relaxed">{service.description}</p>
                  </div>

                  {/* Amenities */}
                  <div className="glass rounded-2xl p-8 border border-border/20">
                    <h2 className="text-xl font-serif font-bold mb-6">Amenities</h2>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {service.amenities.map((amenity, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-primary"></div>
                          <span className="text-sm">{amenity}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}

              {activeTab === 'bookings' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="glass rounded-2xl p-8 border border-border/20"
                >
                  <h2 className="text-xl font-serif font-bold mb-6">Recent Bookings</h2>
                  <div className="space-y-4">
                    {service.recentBookings.map((booking) => (
                      <div key={booking.id} className="flex items-center justify-between p-4 rounded-xl glass border border-border/10">
                        <div>
                          <h3 className="font-semibold">{booking.guest}</h3>
                          <p className="text-sm text-muted-foreground">{booking.date}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">${booking.amount}</p>
                          <span className="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">
                            {booking.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>
              )}

              {activeTab === 'reviews' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="glass rounded-2xl p-8 border border-border/20"
                >
                  <h2 className="text-xl font-serif font-bold mb-6">Customer Reviews</h2>
                  <div className="space-y-6">
                    {service.reviews.map((review) => (
                      <div key={review.id} className="p-4 rounded-xl glass border border-border/10">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="font-semibold">{review.guest}</h3>
                          <div className="flex items-center gap-1">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < review.rating ? 'text-amber-500 fill-current' : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                        <p className="text-muted-foreground text-sm mb-2">{review.comment}</p>
                        <p className="text-xs text-muted-foreground">{review.date}</p>
                      </div>
                    ))}
                  </div>
                </motion.div>
              )}

              {activeTab === 'analytics' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="glass rounded-2xl p-8 border border-border/20"
                >
                  <h2 className="text-xl font-serif font-bold mb-6">Analytics</h2>
                  <div className="text-center py-12">
                    <TrendingUp className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">Analytics Coming Soon</h3>
                    <p className="text-muted-foreground">
                      Detailed analytics and insights will be available in the next update.
                    </p>
                  </div>
                </motion.div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Service Details */}
              <div className="glass rounded-2xl p-6 border border-border/20">
                <h3 className="font-serif font-bold mb-4">Service Details</h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <DollarSign className="w-5 h-5 text-green-600" />
                    <div>
                      <p className="font-semibold">${service.price}</p>
                      <p className="text-sm text-muted-foreground">{service.duration}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-primary" />
                    <div>
                      <p className="font-semibold">{service.location}</p>
                      <p className="text-sm text-muted-foreground">Location</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Users className="w-5 h-5 text-primary" />
                    <div>
                      <p className="font-semibold">Up to {service.capacity} guests</p>
                      <p className="text-sm text-muted-foreground">Capacity</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="glass rounded-2xl p-6 border border-border/20">
                <h3 className="font-serif font-bold mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <button className="w-full px-4 py-2 rounded-full glass border border-border/20 text-foreground font-medium hover:border-primary/40 transition-colors flex items-center justify-center gap-2">
                    <MessageSquare className="w-4 h-4" />
                    Message Guests
                  </button>
                  <button className="w-full px-4 py-2 rounded-full glass border border-red-500/20 text-red-600 font-medium hover:border-red-500/40 transition-colors flex items-center justify-center gap-2">
                    <Trash2 className="w-4 h-4" />
                    Delete Service
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
