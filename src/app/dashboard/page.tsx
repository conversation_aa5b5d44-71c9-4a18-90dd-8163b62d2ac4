'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import {
  Calendar,
  CreditCard,
  Star,
  MapPin,
  Phone,
  Settings,
  Bell,
  User,
  LogOut,
  ArrowRight,
  Sparkles,
  Award
} from 'lucide-react';

const recentBookings = [
  {
    id: 1,
    service: 'Private Jet Charter',
    destination: 'New York → Paris',
    date: '2024-02-15',
    status: 'Confirmed',
    amount: '$15,000',
    image: 'https://images.unsplash.com/photo-1559450314-5e465b1e3a3b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
  },
  {
    id: 2,
    service: 'Luxury Hotel',
    destination: 'Four Seasons Paris',
    date: '2024-02-16',
    status: 'Pending',
    amount: '$2,500',
    image: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
  },
  {
    id: 3,
    service: 'Fine Dining',
    destination: 'Le Bernardin NYC',
    date: '2024-02-20',
    status: 'Confirmed',
    amount: '$800',
    image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
  }
];

const quickActions = [
  { icon: Calendar, label: 'Book Service', href: '/booking' },
  { icon: Sparkles, label: 'My Services', href: '/dashboard/services' },
  { icon: Phone, label: 'Call Concierge', href: '/contact' },
  { icon: Star, label: 'Recommendations', href: '/services' },
  { icon: Settings, label: 'Subscription', href: '/dashboard/subscription' },
  { icon: User, label: 'Profile', href: '/profile' }
];

const stats = [
  { label: 'Total Bookings', value: '24', icon: Calendar },
  { label: 'Loyalty Points', value: '12,450', icon: Award },
  { label: 'Saved Amount', value: '$5,200', icon: CreditCard },
  { label: 'Member Since', value: '2023', icon: Star }
];

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="glass border-b border-border/20 sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center gap-3">
              <Image src="/logo.png" alt="LXGO Logo" width={40} height={40} className="drop-shadow-lg" />
              <span className="text-xl font-serif font-bold text-luxury">LXGO Concierge</span>
            </Link>
            
            <div className="flex items-center gap-4">
              <button className="p-2 rounded-full glass border border-border/20 hover:border-primary/30 transition-colors">
                <Bell className="w-5 h-5" />
              </button>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full gradient-luxury flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                <div className="hidden md:block">
                  <p className="font-medium">John Doe</p>
                  <p className="text-sm text-muted-foreground">Premium Member</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <h1 className="text-3xl md:text-4xl font-serif font-bold mb-2">
            Welcome back, <span className="text-luxury">John</span>
          </h1>
          <p className="text-muted-foreground">
            Your luxury lifestyle dashboard is ready. What would you like to experience today?
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8"
        >
          {stats.map((stat, index) => (
            <div key={index} className="glass rounded-xl p-6 border border-border/20 text-center">
              <div className="w-12 h-12 mx-auto mb-4 rounded-full gradient-luxury flex items-center justify-center">
                <stat.icon className="w-6 h-6 text-white" />
              </div>
              <div className="text-2xl font-bold text-luxury mb-1">{stat.value}</div>
              <div className="text-sm text-muted-foreground">{stat.label}</div>
            </div>
          ))}
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-8"
        >
          <h2 className="text-2xl font-serif font-bold mb-6">Quick Actions</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {quickActions.map((action, index) => (
              <Link
                key={index}
                href={action.href}
                className="glass rounded-xl p-6 border border-border/20 hover:border-primary/30 transition-all duration-300 hover:scale-105 text-center group"
              >
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <action.icon className="w-6 h-6 text-primary" />
                </div>
                <span className="font-medium">{action.label}</span>
              </Link>
            ))}
          </div>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Recent Bookings */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="lg:col-span-2"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-serif font-bold">Recent Bookings</h2>
              <Link href="/bookings" className="text-primary hover:text-primary/80 transition-colors flex items-center gap-2">
                View All
                <ArrowRight className="w-4 h-4" />
              </Link>
            </div>
            
            <div className="space-y-4">
              {recentBookings.map((booking) => (
                <div key={booking.id} className="glass rounded-xl p-6 border border-border/20 hover:border-primary/30 transition-colors">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 rounded-xl overflow-hidden flex-shrink-0">
                      <Image
                        src={booking.image}
                        alt={booking.service}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold mb-1">{booking.service}</h3>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <MapPin className="w-4 h-4" />
                        <span>{booking.destination}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Calendar className="w-4 h-4" />
                        <span>{new Date(booking.date).toLocaleDateString()}</span>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="font-semibold text-lg mb-2">{booking.amount}</div>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        booking.status === 'Confirmed' 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                      }`}>
                        {booking.status}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Sidebar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-6"
          >
            {/* Concierge Card */}
            <div className="glass rounded-xl p-6 border border-border/20">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 rounded-full gradient-luxury flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold">Personal Concierge</h3>
                  <p className="text-sm text-muted-foreground">Available 24/7</p>
                </div>
              </div>
              <p className="text-sm text-muted-foreground mb-4">
                Need assistance with your next luxury experience? Our dedicated concierge team is here to help.
              </p>
              <button className="w-full px-4 py-2 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform">
                Contact Concierge
              </button>
            </div>

            {/* Recommendations */}
            <div className="glass rounded-xl p-6 border border-border/20">
              <h3 className="font-semibold mb-4">Recommended for You</h3>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-lg overflow-hidden">
                    <Image
                      src="https://images.unsplash.com/photo-1563720223185-11003d516935?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                      alt="Luxury car"
                      width={48}
                      height={48}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm">Exotic Car Rental</p>
                    <p className="text-xs text-muted-foreground">From $500/day</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-lg overflow-hidden">
                    <Image
                      src="https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                      alt="Event planning"
                      width={48}
                      height={48}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm">Event Planning</p>
                    <p className="text-xs text-muted-foreground">From $5,000</p>
                  </div>
                </div>
              </div>
              
              <Link
                href="/services"
                className="block w-full mt-4 px-4 py-2 rounded-full glass border border-primary/20 text-center font-medium hover:border-primary/40 transition-colors"
              >
                View All Services
              </Link>
            </div>

            {/* Account Actions */}
            <div className="glass rounded-xl p-6 border border-border/20">
              <h3 className="font-semibold mb-4">Account</h3>
              <div className="space-y-3">
                <Link href="/profile" className="flex items-center gap-3 text-sm hover:text-primary transition-colors">
                  <User className="w-4 h-4" />
                  Edit Profile
                </Link>
                <Link href="/settings" className="flex items-center gap-3 text-sm hover:text-primary transition-colors">
                  <Settings className="w-4 h-4" />
                  Settings
                </Link>
                <button className="flex items-center gap-3 text-sm hover:text-destructive transition-colors">
                  <LogOut className="w-4 h-4" />
                  Sign Out
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
