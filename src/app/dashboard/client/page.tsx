'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Calendar, Heart, Star, Clock, MapPin, Bell, User, CreditCard, Settings, LogOut } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

// Mock data
const upcomingBookings = [
  {
    id: 1,
    title: 'Luxury Beachfront Villa',
    date: 'Jun 15 - 20, 2024',
    location: 'Malibu, California',
    image: 'https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=1471&q=80',
    status: 'Confirmed',
  },
  {
    id: 2,
    title: 'Sunset Yacht Charter',
    date: 'Jul 5, 2024',
    location: 'Miami, Florida',
    image: 'https://images.unsplash.com/photo-1501785888041-af3ef285b470?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
    status: 'Upcoming',
  },
];

const favorites = [
  {
    id: 3,
    title: 'Luxury Car Rental',
    price: 350,
    rating: 4.95,
    reviews: 210,
    image: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
  },
  {
    id: 4,
    title: 'Private Jet to Aspen',
    price: 15000,
    rating: 5.0,
    reviews: 45,
    image: 'https://images.unsplash.com/photo-1436491865333-4b283dc4429e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
  },
];

// Quick action items
const quickActions = [
  {
    title: 'Book Service',
    icon: Calendar,
    href: '/marketplace',
    description: 'Find and book luxury services and experiences',
    color: 'bg-blue-100 text-blue-600',
  },
  {
    title: 'My Services',
    icon: Heart,
    href: '/dashboard/services',
    description: 'View and manage your booked services',
    color: 'bg-pink-100 text-pink-600',
  },
  {
    title: 'Call Concierge',
    icon: 'phone',
    href: 'tel:+1234567890',
    description: 'Get personalized assistance from our concierge',
    color: 'bg-green-100 text-green-600',
  },
  {
    title: 'Recommendations',
    icon: Star,
    href: '/dashboard/recommendations',
    description: 'Discover personalized recommendations',
    color: 'bg-yellow-100 text-yellow-600',
  },
  {
    title: 'Subscription',
    icon: 'credit-card',
    href: '/dashboard/subscription',
    description: 'Manage your membership and billing',
    color: 'bg-purple-100 text-purple-600',
  },
  {
    title: 'Profile',
    icon: User,
    href: '/dashboard/profile',
    description: 'Update your personal information',
    color: 'bg-indigo-100 text-indigo-600',
  },
];

export default function ClientDashboard() {
  const [activeTab, setActiveTab] = useState<'upcoming' | 'favorites' | 'past' | 'payments' | 'settings'>('upcoming');
  const router = useRouter();

  const handleLogout = () => {
    // Handle logout logic
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
          <div className="flex items-center
          ">
            <Link href="/" className="flex items-center">
              <Image 
                src="/logo.png" 
                alt="LXGO Concierge" 
                width={40} 
                height={40} 
                className="h-10 w-auto"
              />
              <span className="ml-2 text-xl font-semibold">LXGO Concierge</span>
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            <button className="p-2 rounded-full hover:bg-gray-100">
              <Bell className="h-6 w-6 text-gray-500" />
            </button>
            <div className="relative">
              <button className="flex items-center space-x-2 focus:outline-none">
                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <User className="h-5 w-5 text-primary" />
                </div>
                <span className="hidden md:inline-block font-medium">John D.</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Sidebar */}
          <div className="w-full md:w-64 flex-shrink-0">
            <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <User className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h2 className="font-semibold">John Doe</h2>
                  <p className="text-sm text-gray-500">Premium Member</p>
                </div>
              </div>
              
              <nav className="space-y-1">
                <button
                  onClick={() => setActiveTab('upcoming')}
                  className={`w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                    activeTab === 'upcoming'
                      ? 'bg-primary/10 text-primary'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Calendar className="mr-3 h-5 w-5" />
                  Upcoming Trips
                </button>
                <button
                  onClick={() => setActiveTab('past')}
                  className={`w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                    activeTab === 'past'
                      ? 'bg-primary/10 text-primary'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Clock className="mr-3 h-5 w-5" />
                  Past Trips
                </button>
                <button
                  onClick={() => setActiveTab('favorites')}
                  className={`w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                    activeTab === 'favorites'
                      ? 'bg-primary/10 text-primary'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Heart className="mr-3 h-5 w-5" />
                  Saved Listings
                </button>
                <button
                  onClick={() => setActiveTab('payments')}
                  className={`w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                    activeTab === 'payments'
                      ? 'bg-primary/10 text-primary'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <CreditCard className="mr-3 h-5 w-5" />
                  Payment Methods
                </button>
                <button
                  onClick={() => setActiveTab('settings')}
                  className={`w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                    activeTab === 'settings'
                      ? 'bg-primary/10 text-primary'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Settings className="mr-3 h-5 w-5" />
                  Account Settings
                </button>
                <button
                  onClick={handleLogout}
                  className="w-full flex items-center px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-lg"
                >
                  <LogOut className="mr-3 h-5 w-5" />
                  Log Out
                </button>
              </nav>
            </div>

            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="font-medium text-gray-900 mb-4">Need help?</h3>
              <p className="text-sm text-gray-600 mb-4">
                Our concierge team is available 24/7 to assist with any questions or special requests.
              </p>
              <button className="w-full bg-primary text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-primary/90 transition-colors">
                Contact Support
              </button>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-6">
                {activeTab === 'upcoming' && 'Upcoming Trips'}
                {activeTab === 'past' && 'Past Trips'}
                {activeTab === 'favorites' && 'Saved Listings'}
                {activeTab === 'payments' && 'Payment Methods'}
                {activeTab === 'settings' && 'Account Settings'}
              </h1>

              {activeTab === 'upcoming' && (
                <div className="space-y-8">
                  {/* Quick Actions Grid */}
                  <div>
                    <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                      {quickActions.map((action, index) => (
                        <Link 
                          key={index}
                          href={action.href}
                          className={`p-6 rounded-xl border border-gray-200 hover:shadow-md transition-shadow ${action.color} flex flex-col items-start`}
                        >
                          <div className="p-3 rounded-lg bg-white/50 backdrop-blur-sm mb-4">
                            {typeof action.icon === 'string' ? (
                              <span className="text-2xl">{action.icon}</span>
                            ) : (
                              <action.icon className="w-6 h-6" />
                            )}
                          </div>
                          <h3 className="text-lg font-semibold mb-1">{action.title}</h3>
                          <p className="text-sm text-gray-600">{action.description}</p>
                        </Link>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h2 className="text-xl font-semibold mb-4">Upcoming Bookings</h2>
                    {upcomingBookings.map((booking) => (
                    <div key={booking.id} className="border border-gray-200 rounded-xl overflow-hidden mb-6">
                      <div className="md:flex">
                        <div className="md:flex-shrink-0 md:w-48 h-48 relative">
                          <Image
                            src={booking.image}
                            alt={booking.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="p-6 flex-1">
                          <div className="flex justify-between">
                            <h3 className="text-lg font-medium text-gray-900">{booking.title}</h3>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              {booking.status}
                            </span>
                          </div>
                          <div className="mt-1 flex items-center text-sm text-gray-500">
                            <Calendar className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" />
                            {booking.date}
                          </div>
                          <div className="mt-1 flex items-center text-sm text-gray-500">
                            <MapPin className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" />
                            {booking.location}
                          </div>
                          <div className="mt-4 flex space-x-3">
                            <button
                              type="button"
                              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                            >
                              View Details
                            </button>
                            <button
                              type="button"
                              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                            >
                              Modify Booking
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'favorites' && (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {favorites.map((item) => (
                    <div key={item.id} className="border border-gray-200 rounded-xl overflow-hidden hover:shadow-md transition-shadow">
                      <div className="h-48 relative">
                        <Image
                          src={item.image}
                          alt={item.title}
                          fill
                          className="object-cover"
                        />
                        <button className="absolute top-3 right-3 p-2 bg-white rounded-full text-gray-700 hover:text-red-500">
                          <Heart className="h-5 w-5" />
                        </button>
                      </div>
                      <div className="p-4">
                        <h3 className="font-medium text-gray-900">{item.title}</h3>
                        <div className="mt-1 flex items-center">
                          <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                          <span className="ml-1 text-sm text-gray-600">
                            {item.rating} ({item.reviews} reviews)
                          </span>
                        </div>
                        <div className="mt-2 flex items-center justify-between">
                          <span className="text-lg font-semibold">${item.price}/day</span>
                          <button className="text-sm font-medium text-primary hover:text-primary/80">
                            Book Now
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'past' && (
                <div className="text-center py-12">
                  <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100">
                    <Clock className="h-6 w-6 text-gray-400" />
                  </div>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    {activeTab === 'past' && 'No past trips'}
                    {activeTab === 'payments' && 'No payment methods'}
                    {activeTab === 'settings' && 'Account settings'}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {activeTab === 'past' && 'Your past trips will appear here.'}
                    {activeTab === 'payments' && 'Add a payment method to get started.'}
                    {activeTab === 'settings' && 'Manage your account settings.'}
                  </p>
                  {activeTab === 'payments' && (
                    <div className="mt-6">
                      <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        Add Payment Method
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
