'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import {
  Crown,
  Check,
  Star,
  Zap,
  Shield
} from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
// import StripeCheckout from '@/components/payment/StripeCheckout';
import { PRICING_TIERS } from '@/lib/stripe';

type PlanType = 'basic' | 'premium' | 'enterprise';

export default function SubscriptionPage() {
  const [currentPlan, setCurrentPlan] = useState<PlanType>('basic');
  const [selectedPlan, setSelectedPlan] = useState<PlanType | null>(null);
  const [showCheckout, setShowCheckout] = useState(false);

  const handleUpgrade = (plan: PlanType) => {
    if (plan === 'basic') return;
    setSelectedPlan(plan);
    setShowCheckout(true);
  };

  const handlePaymentSuccess = (paymentIntent: { id: string }) => {
    console.log('Payment successful:', paymentIntent);
    setCurrentPlan(selectedPlan!);
    setShowCheckout(false);
    setSelectedPlan(null);
    // Here you would update the user's subscription in your database
  };



  const plans = [
    {
      id: 'basic' as PlanType,
      name: 'Basic',
      price: 0,
      period: 'Free',
      icon: Shield,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200',
      features: PRICING_TIERS.basic.features,
      limitations: [
        '15% commission on bookings',
        'Standard support',
        'Basic analytics'
      ]
    },
    {
      id: 'premium' as PlanType,
      name: 'Premium',
      price: 99,
      period: 'per month',
      icon: Crown,
      color: 'text-amber-600',
      bgColor: 'bg-amber-50',
      borderColor: 'border-amber-200',
      features: PRICING_TIERS.premium.features,
      limitations: [
        '10% commission on bookings',
        'Priority support',
        'Advanced analytics'
      ],
      popular: true
    },
    {
      id: 'enterprise' as PlanType,
      name: 'Enterprise',
      price: 299,
      period: 'per month',
      icon: Zap,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      features: PRICING_TIERS.enterprise.features,
      limitations: [
        '5% commission on bookings',
        'Dedicated support',
        'Custom analytics'
      ]
    }
  ];

  if (showCheckout && selectedPlan) {
    const plan = plans.find(p => p.id === selectedPlan)!;
    return (
      <div className="min-h-screen bg-background">
        <Header />
        
        <main className="pt-20">
          <div className="container mx-auto px-6 py-8 max-w-2xl">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-serif font-bold mb-2">Upgrade to {plan.name}</h1>
              <p className="text-muted-foreground">Complete your subscription upgrade</p>
            </div>

            <div className="glass rounded-2xl p-8 border border-border/20 text-center">
              <h3 className="text-xl font-serif font-bold mb-4">Payment Integration</h3>
              <p className="text-muted-foreground mb-6">
                Stripe payment integration will be available once you configure your Stripe keys.
              </p>
              <button
                onClick={() => handlePaymentSuccess({ id: 'demo_payment' })}
                className="px-6 py-3 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform"
              >
                Demo Upgrade (Development)
              </button>
            </div>

            <div className="mt-6 text-center">
              <button
                onClick={() => setShowCheckout(false)}
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                ← Back to plans
              </button>
            </div>
          </div>
        </main>

        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-20">
        <div className="container mx-auto px-6 py-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-serif font-bold mb-4">
              Choose Your <span className="text-luxury">Plan</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Unlock premium features and reduce commission rates with our subscription plans
            </p>
          </div>

          {/* Current Plan Status */}
          {currentPlan !== 'basic' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="max-w-4xl mx-auto mb-8"
            >
              <div className="glass rounded-2xl p-6 border border-green-500/20 bg-green-50/10">
                <div className="flex items-center gap-3">
                  <Check className="w-6 h-6 text-green-600" />
                  <div>
                    <h3 className="font-semibold">Current Plan: {plans.find(p => p.id === currentPlan)?.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      Your subscription is active and will renew automatically
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Pricing Plans */}
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {plans.map((plan, index) => (
              <motion.div
                key={plan.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`relative glass rounded-2xl p-8 border transition-all hover-lift ${
                  plan.popular 
                    ? 'border-primary/40 bg-primary/5' 
                    : 'border-border/20'
                } ${
                  currentPlan === plan.id 
                    ? 'ring-2 ring-green-500/20' 
                    : ''
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="px-4 py-1 rounded-full gradient-luxury text-white text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}

                {currentPlan === plan.id && (
                  <div className="absolute -top-3 right-4">
                    <span className="px-3 py-1 rounded-full bg-green-500 text-white text-xs font-medium">
                      Current
                    </span>
                  </div>
                )}

                <div className="text-center mb-8">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full ${plan.bgColor} border ${plan.borderColor} flex items-center justify-center`}>
                    <plan.icon className={`w-8 h-8 ${plan.color}`} />
                  </div>
                  <h3 className="text-2xl font-serif font-bold mb-2">{plan.name}</h3>
                  <div className="mb-4">
                    <span className="text-4xl font-bold">${plan.price}</span>
                    {plan.price > 0 && <span className="text-muted-foreground">/{plan.period}</span>}
                  </div>
                </div>

                <div className="space-y-4 mb-8">
                  <div>
                    <h4 className="font-semibold mb-3">Features:</h4>
                    <ul className="space-y-2">
                      {plan.features.map((feature, i) => (
                        <li key={i} className="flex items-start gap-2 text-sm">
                          <Check className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-3">Commission & Support:</h4>
                    <ul className="space-y-2">
                      {plan.limitations.map((limitation, i) => (
                        <li key={i} className="flex items-start gap-2 text-sm">
                          <Star className="w-4 h-4 text-amber-500 flex-shrink-0 mt-0.5" />
                          <span>{limitation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <button
                  onClick={() => handleUpgrade(plan.id)}
                  disabled={currentPlan === plan.id}
                  className={`w-full px-6 py-3 rounded-full font-semibold transition-all ${
                    currentPlan === plan.id
                      ? 'bg-green-100 text-green-700 cursor-not-allowed'
                      : plan.id === 'basic'
                      ? 'glass border border-border/20 text-foreground hover:border-primary/40'
                      : 'gradient-luxury text-white hover:scale-105'
                  }`}
                >
                  {currentPlan === plan.id 
                    ? 'Current Plan' 
                    : plan.id === 'basic' 
                    ? 'Downgrade' 
                    : `Upgrade to ${plan.name}`
                  }
                </button>
              </motion.div>
            ))}
          </div>

          {/* FAQ Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="max-w-4xl mx-auto mt-16"
          >
            <h2 className="text-3xl font-serif font-bold text-center mb-8">
              Frequently Asked Questions
            </h2>
            
            <div className="space-y-6">
              <div className="glass rounded-xl p-6 border border-border/20">
                <h3 className="font-semibold mb-2">How do commission rates work?</h3>
                <p className="text-muted-foreground text-sm">
                  Commission rates are applied to each successful booking. Premium and Enterprise plans 
                  offer reduced rates, helping you keep more of your earnings.
                </p>
              </div>
              
              <div className="glass rounded-xl p-6 border border-border/20">
                <h3 className="font-semibold mb-2">Can I cancel my subscription anytime?</h3>
                <p className="text-muted-foreground text-sm">
                  Yes, you can cancel your subscription at any time. Your plan will remain active 
                  until the end of your current billing period.
                </p>
              </div>
              
              <div className="glass rounded-xl p-6 border border-border/20">
                <h3 className="font-semibold mb-2">What happens to my listings if I downgrade?</h3>
                <p className="text-muted-foreground text-sm">
                  Your listings will remain active, but premium features like featured placement 
                  and priority search will be removed.
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
