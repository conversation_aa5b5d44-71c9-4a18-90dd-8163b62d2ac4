'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  MapPin, 
  Clock, 
  Users, 
  Heart, 
  Star, 
  ArrowRight,
  Briefcase,
  Globe,
  Award,
  Zap
} from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

const benefits = [
  {
    icon: Heart,
    title: 'Health & Wellness',
    description: 'Comprehensive health insurance, mental health support, and wellness programs.'
  },
  {
    icon: Clock,
    title: 'Flexible Schedule',
    description: 'Work-life balance with flexible hours and remote work opportunities.'
  },
  {
    icon: Globe,
    title: 'Global Opportunities',
    description: 'Work with international clients and travel opportunities worldwide.'
  },
  {
    icon: Award,
    title: 'Growth & Development',
    description: 'Continuous learning opportunities and career advancement programs.'
  }
];

const openPositions = [
  {
    title: 'Senior Concierge Specialist',
    department: 'Client Services',
    location: 'New York, NY',
    type: 'Full-time',
    description: 'Lead luxury concierge services for high-net-worth clients worldwide.'
  },
  {
    title: 'Luxury Travel Coordinator',
    department: 'Travel Services',
    location: 'London, UK',
    type: 'Full-time',
    description: 'Coordinate exclusive travel experiences and manage partner relationships.'
  },
  {
    title: 'Business Development Manager',
    department: 'Sales',
    location: 'Dubai, UAE',
    type: 'Full-time',
    description: 'Expand our luxury partner network and develop new market opportunities.'
  },
  {
    title: 'UX/UI Designer',
    department: 'Technology',
    location: 'Remote',
    type: 'Full-time',
    description: 'Design exceptional user experiences for our luxury platform.'
  }
];

export default function CareersPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-transparent to-secondary/5" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,oklch(0.55_0.18_180_/_0.08),transparent_50%)]" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="max-w-4xl mx-auto text-center"
            >
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-serif font-bold mb-6">
                <span className="block">Join Our</span>
                <span className="text-luxury">Elite Team</span>
              </h1>
              <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12">
                Shape the future of luxury lifestyle services and create extraordinary 
                experiences for discerning clients worldwide.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Why Work <span className="text-luxury">With Us</span>?
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Join a team that values excellence, innovation, and the pursuit of perfection.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center group"
                >
                  <div className="w-20 h-20 mx-auto mb-6 rounded-full glass border border-primary/20 flex items-center justify-center group-hover:border-primary/40 transition-colors">
                    <benefit.icon className="w-10 h-10 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-4 font-serif">{benefit.title}</h3>
                  <p className="text-muted-foreground">{benefit.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Open Positions */}
        <section className="py-20 relative">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Open <span className="text-luxury">Positions</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Discover exciting opportunities to grow your career in luxury services.
              </p>
            </motion.div>

            <div className="max-w-4xl mx-auto space-y-6">
              {openPositions.map((position, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-8 border border-border/20 hover:border-primary/30 transition-all hover-lift group"
                >
                  <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                    <div>
                      <h3 className="text-2xl font-semibold mb-2 font-serif">{position.title}</h3>
                      <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <Briefcase className="w-4 h-4" />
                          <span>{position.department}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4" />
                          <span>{position.location}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4" />
                          <span>{position.type}</span>
                        </div>
                      </div>
                    </div>
                    <button className="mt-4 md:mt-0 px-6 py-3 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform flex items-center gap-2">
                      Apply Now
                      <ArrowRight className="w-4 h-4" />
                    </button>
                  </div>
                  <p className="text-muted-foreground">{position.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 relative">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 gradient-luxury opacity-10" />
          </div>
          
          <div className="container mx-auto px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="max-w-3xl mx-auto"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Don't See Your <span className="text-luxury">Perfect Role</span>?
              </h2>
              <p className="text-xl text-muted-foreground mb-8">
                We're always looking for exceptional talent. Send us your resume and 
                let us know how you'd like to contribute to our mission.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/contact"
                  className="px-8 py-4 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg"
                >
                  Get in Touch
                </Link>
                <Link
                  href="/about"
                  className="px-8 py-4 rounded-full glass border border-primary/20 text-foreground font-semibold hover:border-primary/40 transition-colors"
                >
                  Learn More About Us
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
