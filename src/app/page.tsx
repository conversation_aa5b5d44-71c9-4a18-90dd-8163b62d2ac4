'use client';

import { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { motion } from 'framer-motion';

// Import sections with dynamic loading for better performance
const HeroSection = dynamic(() => import('@/components/home/<USER>'), { ssr: true });
const ValuePropsSection = dynamic(() => import('@/components/home/<USER>'), { ssr: true });

// Import preview sections with dynamic loading
const MarketplaceSection = dynamic(() => import('@/components/home/<USER>'), { 
  ssr: true,
  loading: () => <div className="h-[600px] bg-muted/20 rounded-xl animate-pulse" />
});

const StaysSection = dynamic(() => import('@/components/home/<USER>'), { 
  ssr: true,
  loading: () => <div className="h-[600px] bg-muted/20 rounded-xl animate-pulse" />
});

const BoatsSection = dynamic(() => import('@/components/home/<USER>'), { 
  ssr: true,
  loading: () => <div className="h-[600px] bg-muted/20 rounded-xl animate-pulse" />
});

const VehiclesSection = dynamic(() => import('@/components/home/<USER>'), { 
  ssr: true,
  loading: () => <div className="h-[600px] bg-muted/20 rounded-xl animate-pulse" />
});

const DealsSection = dynamic(() => import('@/components/home/<USER>'), { 
  ssr: true,
  loading: () => <div className="h-[600px] bg-muted/20 rounded-xl animate-pulse" />
});

const JobsSection = dynamic(() => import('@/components/home/<USER>'), { 
  ssr: true,
  loading: () => <div className="h-[600px] bg-muted/20 rounded-xl animate-pulse" />
});

const LuxurySection = dynamic(() => import('@/components/home/<USER>'), { 
  ssr: true,
  loading: () => <div className="h-[600px] bg-muted/20 rounded-xl animate-pulse" />
});

const ConciergeAISection = dynamic(() => import('@/components/home/<USER>'), { 
  ssr: true,
  loading: () => <div className="h-[600px] bg-muted/20 rounded-xl animate-pulse" />
});

const ScrollToTop = dynamic(() => import('@/components/ui/ScrollToTopWrapper'), { ssr: false });

// Section wrapper for consistent spacing and animation
const SectionWrapper = ({ 
  children, 
  id, 
  className = '',
  containerClass = ''
}: { 
  children: React.ReactNode; 
  id?: string; 
  className?: string;
  containerClass?: string;
}) => {
  return (
    <motion.section 
      id={id} 
      className={`relative overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.6, ease: [0.16, 1, 0.3, 1] }}
    >
      <div className={`w-full px-4 sm:px-6 lg:px-8 mx-auto ${containerClass}`}>
        {children}
      </div>
    </motion.section>
  );
};

// Background pattern component for luxurious styling
const BackgroundPattern = () => (
  <div className="fixed inset-0 -z-10 overflow-hidden">
    {/* Base gradient with teal and amber */}
    <div className="absolute inset-0 bg-gradient-to-br from-background via-background/95 to-background/90" />

    {/* Subtle luxury gradients */}
    <div className="absolute inset-0 bg-gradient-to-br from-teal-50/30 via-transparent to-amber-50/20" />

    {/* Radial gradients for depth */}
    <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_30%,oklch(0.55_0.18_180_/_0.08),transparent_50%)]" />
    <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_70%,oklch(0.68_0.18_50_/_0.06),transparent_50%)]" />

    {/* Subtle mesh pattern */}
    <div className="absolute inset-0 bg-[linear-gradient(to_right,oklch(0.55_0.18_180_/_0.02)_1px,transparent_1px),linear-gradient(to_bottom,oklch(0.55_0.18_180_/_0.02)_1px,transparent_1px)] bg-[size:40px_40px]" />

    {/* Floating orbs for luxury feel */}
    <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-radial from-teal-200/10 to-transparent rounded-full blur-3xl animate-float" />
    <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-radial from-amber-200/10 to-transparent rounded-full blur-3xl animate-float" style={{ animationDelay: '1s' }} />
  </div>
);

export default function Home() {
  return (
    <div className="min-h-screen bg-background text-foreground antialiased">
      {/* Background Pattern */}
      <BackgroundPattern />

      {/* Main Content */}
      <main className="relative z-10 overflow-hidden">
        <Suspense fallback={
          <div className="w-full h-screen flex items-center justify-center">
            <div className="animate-pulse flex space-x-4">
              <div className="h-12 w-12 rounded-full bg-foreground/10"></div>
            </div>
          </div>
        }>
          {/* Hero Section (Full Viewport) */}
          <section className="min-h-[90vh] w-full flex items-center relative">
            <div className="w-full">
              <HeroSection />
            </div>
          </section>

          {/* Main Content Sections */}
          <div className="w-full -mt-20">
            {/* Value Propositions */}
            <SectionWrapper
              id="why-choose-us"
              className="pt-32 pb-20 md:pt-40 md:pb-28 relative"
              containerClass="max-w-7xl mx-auto relative z-10"
            >
              <div className="absolute inset-0 -z-10">
                <div className="absolute inset-0 bg-gradient-to-b from-background/0 via-teal-50/20 to-background/80" />
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,oklch(0.55_0.18_180_/_0.05),transparent_70%)]" />
              </div>
              <ValuePropsSection />
            </SectionWrapper>

            {/* Marketplace */}
            <SectionWrapper
              id="marketplace"
              className="py-24 relative -mt-20"
              containerClass="max-w-7xl mx-auto relative z-10"
            >
              <div className="absolute inset-0 -z-10 glass [mask-image:linear-gradient(to_bottom,transparent_0%,white_5%,white_95%,transparent_100%)]">
                <div className="absolute inset-0 bg-gradient-to-b from-teal-100/10 via-amber-100/5 to-teal-100/10" />
              </div>
              <MarketplaceSection />
            </SectionWrapper>
            
            {/* Stays & Rentals */}
            <SectionWrapper 
              id="stays" 
              className="py-28 relative"
              containerClass="max-w-7xl mx-auto"
            >
              <StaysSection />
            </SectionWrapper>
            
            {/* Boats & Yachts */}
            <SectionWrapper
              id="experiences"
              className="py-24 relative -mt-12"
              containerClass="max-w-7xl mx-auto relative z-10"
            >
              <div className="absolute inset-0 -z-10 glass-teal [mask-image:linear-gradient(to_bottom,transparent_0%,white_5%,white_95%,transparent_100%)]">
                <div className="absolute inset-0 bg-gradient-to-b from-amber-100/8 via-teal-100/12 to-amber-100/8" />
              </div>
              <BoatsSection />
            </SectionWrapper>
            
            {/* Luxury Vehicles */}
            <SectionWrapper 
              id="vehicles" 
              className="py-28 relative"
              containerClass="max-w-7xl mx-auto"
            >
              <VehiclesSection />
            </SectionWrapper>
            
            {/* Exclusive Deals */}
            <SectionWrapper
              id="deals"
              className="py-24 relative -mt-12"
              containerClass="max-w-7xl mx-auto relative z-10"
            >
              <div className="absolute inset-0 -z-10 glass-amber [mask-image:linear-gradient(to_bottom,transparent_0%,white_5%,white_95%,transparent_100%)]">
                <div className="absolute inset-0 bg-gradient-to-b from-teal-100/8 via-amber-100/12 to-teal-100/8" />
              </div>
              <DealsSection />
            </SectionWrapper>
            
            {/* Premium Services */}
            <SectionWrapper 
              id="services" 
              className="py-28 relative"
              containerClass="max-w-7xl mx-auto"
            >
              <JobsSection />
            </SectionWrapper>
            
            {/* Luxury Collection */}
            <SectionWrapper
              id="luxury"
              className="py-24 relative -mt-12"
              containerClass="max-w-7xl mx-auto relative z-10"
            >
              <div className="absolute inset-0 -z-10 glass [mask-image:linear-gradient(to_bottom,transparent_0%,white_5%,white_95%,transparent_100%)]">
                <div className="absolute inset-0 gradient-luxury-radial opacity-10" />
              </div>
              <LuxurySection />
            </SectionWrapper>
            
            {/* AI Concierge */}
            <SectionWrapper
              id="concierge"
              className="relative py-32 -mt-12"
              containerClass="max-w-7xl mx-auto relative z-10"
            >
              <div className="absolute inset-0 -z-10">
                <div className="absolute inset-0 bg-gradient-to-b from-background/0 via-amber-50/30 to-background/90" />
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,oklch(0.68_0.18_50_/_0.12),transparent_70%)]" />
              </div>
              <ConciergeAISection />
            </SectionWrapper>
          </div>
        </Suspense>
      </main>
      
      {/* Scroll to Top Button */}
      <ScrollToTop />
    </div>
  );
}
