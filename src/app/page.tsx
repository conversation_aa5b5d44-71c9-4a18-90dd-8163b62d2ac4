'use client';

import { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { motion } from 'framer-motion';

// Import sections with dynamic loading for better performance
const Header = dynamic(() => import('@/components/home/<USER>'), { ssr: true });
const HeroSection = dynamic(() => import('@/components/home/<USER>'), { ssr: true });
const ValuePropsSection = dynamic(() => import('@/components/home/<USER>'), { ssr: true });
const MarketplaceSection = dynamic(() => import('@/components/home/<USER>'), { ssr: true });
const StaysSection = dynamic(() => import('@/components/home/<USER>'), { ssr: true });
const BoatsSection = dynamic(() => import('@/components/home/<USER>'), { ssr: true });
const VehiclesSection = dynamic(() => import('@/components/home/<USER>'), { ssr: true });
const DealsSection = dynamic(() => import('@/components/home/<USER>'), { ssr: true });
const JobsSection = dynamic(() => import('@/components/home/<USER>'), { ssr: true });
const LuxurySection = dynamic(() => import('@/components/home/<USER>'), { ssr: true });
const ConciergeAISection = dynamic(() => import('@/components/home/<USER>'), { ssr: true });
const FooterSection = dynamic(() => import('@/components/home/<USER>'), { ssr: true });
const ScrollToTop = dynamic(() => import('@/components/ui/ScrollToTopWrapper'), { ssr: false });

// Loading component for suspense fallback
function SectionLoader() {
  return (
    <div className="w-full h-screen flex items-center justify-center">
      <div className="animate-pulse flex space-x-4">
        <div className="h-12 w-12 rounded-full bg-foreground/10"></div>
      </div>
    </div>
  );
}

// Section wrapper for consistent spacing and animation
const SectionWrapper = ({ 
  children, 
  id, 
  className = '',
  containerClass = ''
}: { 
  children: React.ReactNode; 
  id?: string; 
  className?: string;
  containerClass?: string;
}) => {
  return (
    <motion.section 
      id={id} 
      className={`relative overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.6, ease: [0.16, 1, 0.3, 1] }}
    >
      <div className={`w-full px-4 sm:px-6 lg:px-8 mx-auto ${containerClass}`}>
        {children}
      </div>
    </motion.section>
  );
};

// Background pattern component for consistent styling
const BackgroundPattern = () => (
  <div className="fixed inset-0 -z-10 overflow-hidden">
    <div className="absolute inset-0 bg-gradient-to-b from-background via-background/80 to-background/30" />
    <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]" />
    <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(120,119,198,0.1),transparent_70%)]" />
  </div>
);

export default function Home() {
  return (
    <div className="min-h-screen bg-background text-foreground antialiased">
      {/* Background Pattern */}
      <BackgroundPattern />

      {/* Header */}
      <Header />

      {/* Main Content */}
      <main className="relative z-10 overflow-hidden">
        <Suspense fallback={<SectionLoader />}>
          {/* Hero Section (Full Viewport) */}
          <section className="min-h-[90vh] w-full flex items-center relative">
            <div className="w-full">
              <HeroSection />
            </div>
          </section>

          {/* Main Content Sections */}
          <div className="w-full -mt-20">
            {/* Value Propositions */}
            <SectionWrapper 
              id="why-choose-us" 
              className="pt-32 pb-20 md:pt-40 md:pb-28 relative"
              containerClass="max-w-7xl mx-auto relative z-10"
            >
              <div className="absolute inset-0 -z-10">
                <div className="absolute inset-0 bg-gradient-to-b from-background/0 via-background/30 to-background/80" />
                <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-[0.02]" />
              </div>
              <ValuePropsSection />
            </SectionWrapper>

            {/* Marketplace */}
            <SectionWrapper 
              id="marketplace" 
              className="py-24 relative -mt-20"
              containerClass="max-w-7xl mx-auto relative z-10"
            >
              <div className="absolute inset-0 -z-10 bg-background/80 backdrop-blur-sm [mask-image:linear-gradient(to_bottom,transparent_0%,white_5%,white_95%,transparent_100%)]">
                <div className="absolute inset-0 bg-gradient-to-b from-border/5 via-border/10 to-border/5" />
              </div>
              <MarketplaceSection />
            </SectionWrapper>
            
            {/* Stays & Rentals */}
            <SectionWrapper 
              id="stays" 
              className="py-28 relative"
              containerClass="max-w-7xl mx-auto"
            >
              <StaysSection />
            </SectionWrapper>
            
            {/* Boats & Yachts */}
            <SectionWrapper 
              id="experiences" 
              className="py-24 relative -mt-12"
              containerClass="max-w-7xl mx-auto relative z-10"
            >
              <div className="absolute inset-0 -z-10 bg-background/80 backdrop-blur-sm [mask-image:linear-gradient(to_bottom,transparent_0%,white_5%,white_95%,transparent_100%)]">
                <div className="absolute inset-0 bg-gradient-to-b from-border/5 via-border/10 to-border/5" />
              </div>
              <BoatsSection />
            </SectionWrapper>
            
            {/* Luxury Vehicles */}
            <SectionWrapper 
              id="vehicles" 
              className="py-28 relative"
              containerClass="max-w-7xl mx-auto"
            >
              <VehiclesSection />
            </SectionWrapper>
            
            {/* Exclusive Deals */}
            <SectionWrapper 
              id="deals" 
              className="py-24 relative -mt-12"
              containerClass="max-w-7xl mx-auto relative z-10"
            >
              <div className="absolute inset-0 -z-10 bg-background/80 backdrop-blur-sm [mask-image:linear-gradient(to_bottom,transparent_0%,white_5%,white_95%,transparent_100%)]">
                <div className="absolute inset-0 bg-gradient-to-b from-border/5 via-border/10 to-border/5" />
              </div>
              <DealsSection />
            </SectionWrapper>
            
            {/* Premium Services */}
            <SectionWrapper 
              id="services" 
              className="py-28 relative"
              containerClass="max-w-7xl mx-auto"
            >
              <JobsSection />
            </SectionWrapper>
            
            {/* Luxury Collection */}
            <SectionWrapper 
              id="luxury" 
              className="py-24 relative -mt-12"
              containerClass="max-w-7xl mx-auto relative z-10"
            >
              <div className="absolute inset-0 -z-10 bg-background/80 backdrop-blur-sm [mask-image:linear-gradient(to_bottom,transparent_0%,white_5%,white_95%,transparent_100%)]">
                <div className="absolute inset-0 bg-gradient-to-b from-border/5 via-border/10 to-border/5" />
              </div>
              <LuxurySection />
            </SectionWrapper>
            
            {/* AI Concierge */}
            <SectionWrapper 
              id="concierge" 
              className="relative py-32 -mt-12"
              containerClass="max-w-7xl mx-auto relative z-10"
            >
              <div className="absolute inset-0 -z-10">
                <div className="absolute inset-0 bg-gradient-to-b from-background/0 via-background/60 to-background/90" />
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(var(--primary)/0.08),transparent_70%)]" />
              </div>
              <ConciergeAISection />
            </SectionWrapper>
          </div>
        </Suspense>
      </main>

      {/* Footer */}
      <FooterSection />
      
      {/* Scroll to Top Button */}
      <ScrollToTop />
    </div>
  );
}
