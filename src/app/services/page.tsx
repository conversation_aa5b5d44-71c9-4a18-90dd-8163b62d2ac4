'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { 
  Plane, 
  Car, 
  Home, 
  Utensils, 
  Calendar, 
  Shield, 
  Star, 
  ArrowRight,
  Sparkles,
  Globe,
  Clock,
  Award
} from 'lucide-react';

const services = [
  {
    icon: Plane,
    title: 'Private Aviation',
    description: 'Access to the world\'s most exclusive private jet fleet with global destinations.',
    features: ['On-demand charter', 'Jet card programs', 'Aircraft management', 'Global clearance'],
    image: 'https://images.unsplash.com/photo-1559450314-5e465b1e3a3b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    price: 'From $15,000',
    category: 'Travel'
  },
  {
    icon: Car,
    title: 'Luxury Transportation',
    description: 'Premium ground transportation with chauffeur services and exotic car rentals.',
    features: ['Chauffeur services', 'Exotic car rental', 'Airport transfers', 'Event transportation'],
    image: 'https://images.unsplash.com/photo-1563720223185-11003d516935?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    price: 'From $500',
    category: 'Transportation'
  },
  {
    icon: Home,
    title: 'Luxury Accommodations',
    description: 'Curated selection of the world\'s most exclusive hotels, resorts, and private villas.',
    features: ['5-star hotels', 'Private villas', 'Yacht charters', 'Exclusive resorts'],
    image: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    price: 'From $1,000',
    category: 'Accommodation'
  },
  {
    icon: Utensils,
    title: 'Fine Dining',
    description: 'Reservations at Michelin-starred restaurants and private chef experiences.',
    features: ['Michelin restaurants', 'Private chefs', 'Wine experiences', 'Culinary tours'],
    image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    price: 'From $300',
    category: 'Dining'
  },
  {
    icon: Calendar,
    title: 'Event Planning',
    description: 'Bespoke event planning for weddings, corporate events, and private celebrations.',
    features: ['Wedding planning', 'Corporate events', 'Private parties', 'Venue sourcing'],
    image: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    price: 'From $5,000',
    category: 'Events'
  },
  {
    icon: Sparkles,
    title: 'Personal Shopping',
    description: 'Exclusive access to luxury brands and personalized shopping experiences.',
    features: ['Personal stylists', 'Exclusive access', 'Custom pieces', 'Global delivery'],
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    price: 'From $200',
    category: 'Lifestyle'
  }
];

const benefits = [
  {
    icon: Shield,
    title: 'Verified Partners',
    description: 'All our partners are thoroughly vetted for quality and reliability.'
  },
  {
    icon: Clock,
    title: '24/7 Support',
    description: 'Round-the-clock concierge support wherever you are in the world.'
  },
  {
    icon: Globe,
    title: 'Global Network',
    description: 'Access to exclusive experiences in over 100 destinations worldwide.'
  },
  {
    icon: Award,
    title: 'Exclusive Access',
    description: 'Members-only experiences not available to the general public.'
  }
];

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="fixed top-0 left-0 w-full z-50 glass border-b border-border/20">
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center gap-3">
            <Image src="/logo.png" alt="LXGO Logo" width={40} height={40} className="drop-shadow-lg" />
            <span className="text-xl font-serif font-bold text-luxury">LXGO Concierge</span>
          </Link>
          <nav className="hidden md:flex items-center gap-8">
            <Link href="/" className="text-foreground/80 hover:text-primary transition-colors">Home</Link>
            <Link href="/about" className="text-foreground/80 hover:text-primary transition-colors">About</Link>
            <Link href="/contact" className="text-foreground/80 hover:text-primary transition-colors">Contact</Link>
            <Link href="/auth/login" className="px-6 py-2 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform">
              Sign In
            </Link>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-32 pb-20 relative overflow-hidden">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-transparent to-secondary/5" />
          <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-[0.02]" />
        </div>
        
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto text-center"
          >
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-serif font-bold mb-6">
              <span className="block">Premium</span>
              <span className="text-luxury">Services</span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12">
              Discover our comprehensive suite of luxury services designed to elevate 
              every aspect of your lifestyle.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 relative">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {services.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group relative"
              >
                <div className="glass rounded-2xl overflow-hidden border border-border/20 hover:border-primary/30 transition-all duration-500 hover:shadow-2xl">
                  {/* Image */}
                  <div className="aspect-[4/3] relative overflow-hidden">
                    <Image
                      src={service.image}
                      alt={service.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />
                    <div className="absolute top-4 left-4">
                      <span className="px-3 py-1 rounded-full text-xs font-medium glass text-white border border-white/20">
                        {service.category}
                      </span>
                    </div>
                    <div className="absolute bottom-4 right-4">
                      <div className="w-12 h-12 rounded-full gradient-luxury flex items-center justify-center">
                        <service.icon className="w-6 h-6 text-white" />
                      </div>
                    </div>
                  </div>
                  
                  {/* Content */}
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-xl font-semibold">{service.title}</h3>
                      <span className="text-sm font-medium text-primary">{service.price}</span>
                    </div>
                    
                    <p className="text-muted-foreground mb-6">{service.description}</p>
                    
                    <div className="space-y-2 mb-6">
                      {service.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center gap-2 text-sm">
                          <Star className="w-4 h-4 text-secondary fill-current" />
                          <span>{feature}</span>
                        </div>
                      ))}
                    </div>
                    
                    <button className="w-full px-6 py-3 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform flex items-center justify-center gap-2">
                      Learn More
                      <ArrowRight className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 relative">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent" />
        </div>
        
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
              Why Choose <span className="text-luxury">LXGO</span>?
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Experience the difference that comes with true luxury service excellence.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className="w-20 h-20 mx-auto mb-6 rounded-full glass border border-primary/20 flex items-center justify-center group-hover:border-primary/40 transition-colors">
                  <benefit.icon className="w-10 h-10 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-4">{benefit.title}</h3>
                <p className="text-muted-foreground">{benefit.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 gradient-luxury opacity-10" />
        </div>
        
        <div className="container mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
              Ready to <span className="text-luxury">Get Started</span>?
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              Let our expert concierge team curate the perfect experience for you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/booking"
                className="px-8 py-4 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg"
              >
                Book a Service
              </Link>
              <Link
                href="/contact"
                className="px-8 py-4 rounded-full glass border border-primary/20 text-foreground font-semibold hover:border-primary/40 transition-colors"
              >
                Speak to Concierge
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
