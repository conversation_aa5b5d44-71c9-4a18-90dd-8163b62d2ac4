'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import { 
  Calendar, 
  MapPin, 
  Users, 
  Clock, 
  CreditCard,
  ArrowRight,
  Star,
  Shield,
  Check,
  Plane,
  Car,
  Home,
  Utensils
} from 'lucide-react';

const serviceCategories = [
  { id: 'aviation', name: 'Private Aviation', icon: Plane, color: 'from-blue-500 to-blue-600' },
  { id: 'transportation', name: 'Luxury Transportation', icon: Car, color: 'from-purple-500 to-purple-600' },
  { id: 'accommodation', name: 'Luxury Stays', icon: Home, color: 'from-green-500 to-green-600' },
  { id: 'dining', name: 'Fine Dining', icon: Utensils, color: 'from-orange-500 to-orange-600' }
];

const popularServices = [
  {
    id: 1,
    title: 'Private Jet Charter',
    description: 'On-demand private jet service to any destination worldwide',
    price: 'From $15,000',
    rating: 5.0,
    image: 'https://images.unsplash.com/photo-1559450314-5e465b1e3a3b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    category: 'aviation'
  },
  {
    id: 2,
    title: 'Luxury Hotel Suite',
    description: 'Premium accommodations at the world\'s finest hotels',
    price: 'From $1,000',
    rating: 4.9,
    image: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    category: 'accommodation'
  },
  {
    id: 3,
    title: 'Michelin Star Dining',
    description: 'Exclusive reservations at the world\'s best restaurants',
    price: 'From $300',
    rating: 4.8,
    image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    category: 'dining'
  },
  {
    id: 4,
    title: 'Exotic Car Rental',
    description: 'Drive the world\'s most exclusive supercars',
    price: 'From $500',
    rating: 4.9,
    image: 'https://images.unsplash.com/photo-1563720223185-11003d516935?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    category: 'transportation'
  }
];

export default function BookingPage() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [bookingStep, setBookingStep] = useState(1);
  const [selectedService, setSelectedService] = useState<any>(null);
  const [bookingData, setBookingData] = useState({
    date: '',
    time: '',
    guests: 1,
    location: '',
    specialRequests: ''
  });

  const filteredServices = selectedCategory === 'all' 
    ? popularServices 
    : popularServices.filter(service => service.category === selectedCategory);

  const handleServiceSelect = (service: any) => {
    setSelectedService(service);
    setBookingStep(2);
  };

  const handleBookingSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle booking submission
    console.log('Booking submitted:', { service: selectedService, ...bookingData });
    setBookingStep(3);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="glass border-b border-border/20 sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center gap-3">
            <Image src="/logo.png" alt="LXGO Logo" width={40} height={40} className="drop-shadow-lg" />
            <span className="text-xl font-serif font-bold text-luxury">LXGO Concierge</span>
          </Link>
          <nav className="hidden md:flex items-center gap-8">
            <Link href="/dashboard" className="text-foreground/80 hover:text-primary transition-colors">Dashboard</Link>
            <Link href="/services" className="text-foreground/80 hover:text-primary transition-colors">Services</Link>
            <Link href="/contact" className="text-foreground/80 hover:text-primary transition-colors">Contact</Link>
          </nav>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {bookingStep === 1 && (
          <>
            {/* Hero Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h1 className="text-4xl md:text-5xl font-serif font-bold mb-4">
                Book Your <span className="text-luxury">Luxury Experience</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Choose from our curated selection of premium services and let our concierge team 
                create an unforgettable experience tailored just for you.
              </p>
            </motion.div>

            {/* Service Categories */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-serif font-bold mb-6">Service Categories</h2>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <button
                  onClick={() => setSelectedCategory('all')}
                  className={`p-4 rounded-xl border transition-all ${
                    selectedCategory === 'all'
                      ? 'border-primary/40 glass'
                      : 'border-border/20 glass hover:border-primary/30'
                  }`}
                >
                  <div className="text-center">
                    <div className="w-12 h-12 mx-auto mb-3 rounded-full gradient-luxury flex items-center justify-center">
                      <Star className="w-6 h-6 text-white" />
                    </div>
                    <span className="font-medium">All Services</span>
                  </div>
                </button>
                
                {serviceCategories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`p-4 rounded-xl border transition-all ${
                      selectedCategory === category.id
                        ? 'border-primary/40 glass'
                        : 'border-border/20 glass hover:border-primary/30'
                    }`}
                  >
                    <div className="text-center">
                      <div className={`w-12 h-12 mx-auto mb-3 rounded-full bg-gradient-to-r ${category.color} flex items-center justify-center`}>
                        <category.icon className="w-6 h-6 text-white" />
                      </div>
                      <span className="font-medium text-sm">{category.name}</span>
                    </div>
                  </button>
                ))}
              </div>
            </motion.div>

            {/* Services Grid */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-serif font-bold">
                  {selectedCategory === 'all' ? 'Popular Services' : 'Available Services'}
                </h2>
                <span className="text-muted-foreground">{filteredServices.length} services</span>
              </div>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredServices.map((service, index) => (
                  <motion.div
                    key={service.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.1 * index }}
                    className="glass rounded-xl overflow-hidden border border-border/20 hover:border-primary/30 transition-all duration-300 hover:shadow-xl group cursor-pointer"
                    onClick={() => handleServiceSelect(service)}
                  >
                    <div className="aspect-[4/3] relative overflow-hidden">
                      <Image
                        src={service.image}
                        alt={service.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />
                      <div className="absolute top-4 right-4">
                        <div className="flex items-center gap-1 px-2 py-1 rounded-full glass text-white text-xs">
                          <Star className="w-3 h-3 fill-current text-yellow-400" />
                          <span>{service.rating}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-6">
                      <h3 className="font-semibold text-lg mb-2">{service.title}</h3>
                      <p className="text-muted-foreground text-sm mb-4">{service.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="font-semibold text-primary">{service.price}</span>
                        <button className="px-4 py-2 rounded-full gradient-luxury text-white text-sm font-medium hover:scale-105 transition-transform">
                          Select
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </>
        )}

        {bookingStep === 2 && selectedService && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            <div className="mb-8">
              <button
                onClick={() => setBookingStep(1)}
                className="text-primary hover:text-primary/80 transition-colors mb-4"
              >
                ← Back to Services
              </button>
              <h1 className="text-3xl font-serif font-bold mb-2">Book {selectedService.title}</h1>
              <p className="text-muted-foreground">{selectedService.description}</p>
            </div>

            <div className="grid lg:grid-cols-3 gap-8">
              {/* Booking Form */}
              <div className="lg:col-span-2">
                <form onSubmit={handleBookingSubmit} className="space-y-6">
                  <div className="glass rounded-xl p-6 border border-border/20">
                    <h3 className="font-semibold text-lg mb-4">Booking Details</h3>
                    
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium mb-2">Preferred Date</label>
                        <div className="relative">
                          <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                          <input
                            type="date"
                            value={bookingData.date}
                            onChange={(e) => setBookingData({...bookingData, date: e.target.value})}
                            className="w-full pl-10 pr-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                            required
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-2">Preferred Time</label>
                        <div className="relative">
                          <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                          <input
                            type="time"
                            value={bookingData.time}
                            onChange={(e) => setBookingData({...bookingData, time: e.target.value})}
                            className="w-full pl-10 pr-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                            required
                          />
                        </div>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-6 mt-6">
                      <div>
                        <label className="block text-sm font-medium mb-2">Number of Guests</label>
                        <div className="relative">
                          <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                          <select
                            value={bookingData.guests}
                            onChange={(e) => setBookingData({...bookingData, guests: parseInt(e.target.value)})}
                            className="w-full pl-10 pr-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                          >
                            {[1,2,3,4,5,6,7,8].map(num => (
                              <option key={num} value={num}>{num} {num === 1 ? 'Guest' : 'Guests'}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-2">Location/Destination</label>
                        <div className="relative">
                          <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                          <input
                            type="text"
                            value={bookingData.location}
                            onChange={(e) => setBookingData({...bookingData, location: e.target.value})}
                            className="w-full pl-10 pr-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                            placeholder="Enter location"
                            required
                          />
                        </div>
                      </div>
                    </div>

                    <div className="mt-6">
                      <label className="block text-sm font-medium mb-2">Special Requests</label>
                      <textarea
                        value={bookingData.specialRequests}
                        onChange={(e) => setBookingData({...bookingData, specialRequests: e.target.value})}
                        rows={4}
                        className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors resize-none"
                        placeholder="Any special requirements or preferences..."
                      />
                    </div>
                  </div>

                  <button
                    type="submit"
                    className="w-full px-8 py-4 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg flex items-center justify-center gap-2"
                  >
                    Confirm Booking
                    <ArrowRight className="w-5 h-5" />
                  </button>
                </form>
              </div>

              {/* Booking Summary */}
              <div className="space-y-6">
                <div className="glass rounded-xl p-6 border border-border/20">
                  <h3 className="font-semibold text-lg mb-4">Booking Summary</h3>
                  
                  <div className="aspect-[4/3] rounded-lg overflow-hidden mb-4">
                    <Image
                      src={selectedService.image}
                      alt={selectedService.title}
                      width={300}
                      height={200}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <h4 className="font-semibold mb-2">{selectedService.title}</h4>
                  <p className="text-muted-foreground text-sm mb-4">{selectedService.description}</p>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Base Price:</span>
                      <span className="font-medium">{selectedService.price}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Service Fee:</span>
                      <span className="font-medium">$200</span>
                    </div>
                    <div className="border-t border-border/20 pt-2 mt-2">
                      <div className="flex justify-between font-semibold">
                        <span>Total:</span>
                        <span className="text-primary">Contact for Quote</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="glass rounded-xl p-6 border border-border/20">
                  <div className="flex items-center gap-3 mb-4">
                    <Shield className="w-6 h-6 text-primary" />
                    <h3 className="font-semibold">Booking Protection</h3>
                  </div>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-500" />
                      <span>Free cancellation up to 24h</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-500" />
                      <span>24/7 concierge support</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-500" />
                      <span>Satisfaction guarantee</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {bookingStep === 3 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-2xl mx-auto text-center"
          >
            <div className="glass rounded-2xl p-12 border border-border/20">
              <div className="w-20 h-20 mx-auto mb-6 rounded-full gradient-luxury flex items-center justify-center">
                <Check className="w-10 h-10 text-white" />
              </div>
              
              <h1 className="text-3xl font-serif font-bold mb-4">
                Booking <span className="text-luxury">Confirmed!</span>
              </h1>
              
              <p className="text-muted-foreground mb-8">
                Thank you for choosing LXGO Concierge. Our team will contact you within 2 hours 
                to finalize the details of your luxury experience.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/dashboard"
                  className="px-8 py-3 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform"
                >
                  View Dashboard
                </Link>
                <Link
                  href="/booking"
                  className="px-8 py-3 rounded-full glass border border-primary/20 text-foreground font-semibold hover:border-primary/40 transition-colors"
                >
                  Book Another Service
                </Link>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
