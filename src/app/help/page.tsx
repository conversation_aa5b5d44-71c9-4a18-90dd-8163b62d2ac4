'use client';

import { motion } from 'framer-motion';
import { HelpCircle, MessageSquare, Phone, Clock } from 'lucide-react';
import Link from 'next/link';

const faqs = [
  {
    question: 'How do I create an account?',
    answer: 'Click on the "Sign Up" button in the top right corner and follow the prompts to create your LXGO account.'
  },
  {
    question: 'What services does LXGO offer?',
    answer: 'We offer a wide range of luxury concierge services including travel planning, event coordination, restaurant reservations, and exclusive experiences.'
  },
  {
    question: 'How can I contact customer support?',
    answer: 'You can reach our 24/7 support team via phone, email, or live chat. Response times are typically under 15 minutes.'
  },
  {
    question: 'Is there a membership fee?',
    answer: 'We offer different membership tiers to suit your needs. Basic access is free, while premium memberships offer additional benefits and priority service.'
  },
  {
    question: 'How do I make a booking?',
    answer: 'Simply log in to your account, select the service you need, and follow the booking process. Our team will confirm your request shortly.'
  },
  {
    question: 'What is your cancellation policy?',
    answer: 'Cancellation policies vary by service provider. Please refer to the specific terms when making your booking or contact our support team for assistance.'
  }
];

export default function HelpPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-16"
      >
        <h1 className="text-4xl md:text-5xl font-serif font-bold mb-6">
          How can we help you today?
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Find answers to common questions or get in touch with our support team.
        </p>
      </motion.div>

      <div className="max-w-4xl mx-auto mb-20">
        <div className="bg-card rounded-2xl shadow-xl p-8">
          <h2 className="text-2xl font-semibold mb-6 flex items-center gap-3">
            <HelpCircle className="w-6 h-6 text-primary" />
            Frequently Asked Questions
          </h2>
          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="border-b border-border/20 pb-6 last:border-0 last:pb-0"
              >
                <h3 className="text-lg font-medium mb-2">{faq.question}</h3>
                <p className="text-muted-foreground">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto">
        <div className="bg-gradient-to-br from-primary/5 to-secondary/5 rounded-2xl p-8 text-center">
          <h2 className="text-2xl font-semibold mb-6">Still need help?</h2>
          <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
            Our dedicated support team is available 24/7 to assist you with any questions or concerns.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-6">
            <Link 
              href="/contact" 
              className="px-6 py-3 bg-primary text-white rounded-full font-medium hover:bg-primary/90 transition-colors flex items-center justify-center gap-2"
            >
              <MessageSquare className="w-5 h-5" />
              Contact Support
            </Link>
            <Link 
              href="tel:+15551235489" 
              className="px-6 py-3 border border-border rounded-full font-medium hover:bg-accent/50 transition-colors flex items-center justify-center gap-2"
            >
              <Phone className="w-5 h-5" />
              +****************
            </Link>
          </div>
          <div className="mt-6 text-sm text-muted-foreground flex items-center justify-center gap-2">
            <Clock className="w-4 h-4" />
            24/7 Support Available
          </div>
        </div>
      </div>
    </div>
  );
}
