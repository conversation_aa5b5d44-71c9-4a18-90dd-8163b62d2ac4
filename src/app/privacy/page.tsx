'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="glass border-b border-border/20 sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center gap-3">
            <Image src="/logo.png" alt="LXGO Logo" width={40} height={40} className="drop-shadow-lg" />
            <span className="text-xl font-serif font-bold text-luxury">LXGO Concierge</span>
          </Link>
          <nav className="hidden md:flex items-center gap-8">
            <Link href="/" className="text-foreground/80 hover:text-primary transition-colors">Home</Link>
            <Link href="/about" className="text-foreground/80 hover:text-primary transition-colors">About</Link>
            <Link href="/contact" className="text-foreground/80 hover:text-primary transition-colors">Contact</Link>
          </nav>
        </div>
      </header>

      <div className="container mx-auto px-6 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          <h1 className="text-4xl md:text-5xl font-serif font-bold mb-6">
            Privacy <span className="text-luxury">Policy</span>
          </h1>
          <p className="text-muted-foreground text-lg mb-8">
            Last updated: {new Date().toLocaleDateString()}
          </p>

          <div className="prose prose-lg max-w-none">
            <div className="glass rounded-xl p-8 border border-border/20 mb-8">
              <h2 className="text-2xl font-semibold mb-4">Our Commitment to Privacy</h2>
              <p className="text-muted-foreground">
                At LXGO Concierge, we understand that privacy is paramount when dealing with luxury services 
                and personal preferences. This Privacy Policy explains how we collect, use, and protect your 
                personal information when you use our services.
              </p>
            </div>

            <div className="space-y-8">
              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Information We Collect</h2>
                <div className="space-y-4 text-muted-foreground">
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Personal Information</h3>
                    <p>We collect information you provide directly, including name, email, phone number, preferences, and payment information.</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Service Information</h3>
                    <p>Details about your bookings, preferences, special requests, and service history to provide personalized experiences.</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Usage Information</h3>
                    <p>Information about how you interact with our platform, including device information and usage patterns.</p>
                  </div>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">How We Use Your Information</h2>
                <div className="space-y-3 text-muted-foreground">
                  <p>• Provide and personalize our luxury concierge services</p>
                  <p>• Process bookings and manage your account</p>
                  <p>• Communicate with you about services and updates</p>
                  <p>• Improve our services and develop new offerings</p>
                  <p>• Ensure security and prevent fraud</p>
                  <p>• Comply with legal obligations</p>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Information Sharing</h2>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    We do not sell your personal information. We may share information with:
                  </p>
                  <div className="space-y-3">
                    <p>• <strong>Service Partners:</strong> Trusted luxury service providers to fulfill your requests</p>
                    <p>• <strong>Payment Processors:</strong> Secure payment processing companies</p>
                    <p>• <strong>Legal Requirements:</strong> When required by law or to protect our rights</p>
                    <p>• <strong>Business Transfers:</strong> In connection with mergers or acquisitions</p>
                  </div>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Data Security</h2>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    We implement industry-standard security measures to protect your information:
                  </p>
                  <div className="space-y-3">
                    <p>• End-to-end encryption for sensitive data</p>
                    <p>• Secure data centers with 24/7 monitoring</p>
                    <p>• Regular security audits and updates</p>
                    <p>• Limited access on a need-to-know basis</p>
                    <p>• Compliance with international privacy standards</p>
                  </div>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Your Rights</h2>
                <div className="space-y-4 text-muted-foreground">
                  <p>You have the right to:</p>
                  <div className="space-y-3">
                    <p>• Access and review your personal information</p>
                    <p>• Correct inaccurate or incomplete information</p>
                    <p>• Delete your account and personal data</p>
                    <p>• Opt-out of marketing communications</p>
                    <p>• Data portability and export</p>
                    <p>• Lodge complaints with supervisory authorities</p>
                  </div>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">International Transfers</h2>
                <p className="text-muted-foreground">
                  As a global luxury service provider, we may transfer your information internationally. 
                  We ensure appropriate safeguards are in place and comply with applicable data protection laws.
                </p>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Contact Us</h2>
                <div className="text-muted-foreground space-y-4">
                  <p>
                    If you have questions about this Privacy Policy or our data practices, please contact us:
                  </p>
                  <div className="space-y-2">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Phone:</strong> +1 (555) 123-LXGO</p>
                    <p><strong>Address:</strong> 432 Park Avenue, Suite 1200, New York, NY 10016</p>
                  </div>
                </div>
              </section>
            </div>

            <div className="mt-12 text-center">
              <Link
                href="/"
                className="px-8 py-3 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg"
              >
                Return to Home
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
