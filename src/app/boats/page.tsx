import { Metadata } from 'next';
import dynamic from 'next/dynamic';

// Using dynamic import to handle client components
const BoatsSection = dynamic(
  () => import('@/components/home/<USER>'),
  { ssr: true }
);

export const metadata: Metadata = {
  title: 'Yachts & Boats | LXGO Concierge',
  description: 'Charter the finest yachts and boats for your next luxury maritime adventure.',
};

export default function BoatsPage() {
  return (
    <div className="pt-24 pb-16 md:pt-28">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-4">
            Yachts & Boats
          </h1>
          <p className="text-xl text-muted-foreground">
            Set sail in style with our exclusive collection of luxury yachts and boats.
          </p>
        </div>
        
        <BoatsSection />
      </div>
    </div>
  );
}
