import { Metadata } from 'next';
import dynamic from 'next/dynamic';

// Using dynamic import to handle client components
const JobsSection = dynamic(
  () => import('@/components/home/<USER>'),
  { ssr: true }
);

export const metadata: Metadata = {
  title: 'Premium Opportunities | LXGO Concierge',
  description: 'Find premium job opportunities and professional services in the luxury sector.',
};

export default function JobsPage() {
  return (
    <div className="pt-24 pb-16 md:pt-28">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-4">
            Premium Opportunities
          </h1>
          <p className="text-xl text-muted-foreground">
            Connect with top-tier professionals and discover exclusive opportunities.
          </p>
        </div>
        
        <JobsSection />
      </div>
    </div>
  );
}
