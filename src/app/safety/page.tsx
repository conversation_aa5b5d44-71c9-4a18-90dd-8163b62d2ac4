'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  Shield, 
  Lock, 
  Eye, 
  CheckCircle, 
  AlertTriangle,
  Phone,
  Mail,
  Clock,
  Users,
  FileText,
  Globe
} from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

const safetyFeatures = [
  {
    icon: Shield,
    title: 'Verified Partners',
    description: 'All service providers undergo rigorous background checks and verification processes.',
    details: ['Identity verification', 'License validation', 'Insurance confirmation', 'Regular audits']
  },
  {
    icon: Lock,
    title: 'Secure Transactions',
    description: 'Bank-level encryption protects all financial transactions and personal data.',
    details: ['256-bit SSL encryption', 'PCI DSS compliance', 'Fraud monitoring', 'Secure payment processing']
  },
  {
    icon: Eye,
    title: '24/7 Monitoring',
    description: 'Round-the-clock monitoring ensures immediate response to any safety concerns.',
    details: ['Real-time tracking', 'Emergency protocols', 'Instant alerts', 'Global support network']
  },
  {
    icon: Users,
    title: 'Trusted Community',
    description: 'Our community of verified members creates a safe and exclusive environment.',
    details: ['Member verification', 'Review system', 'Community guidelines', 'Moderated interactions']
  }
];

const emergencyContacts = [
  {
    region: 'North America',
    phone: '+1 (555) 911-LXGO',
    email: '<EMAIL>',
    hours: '24/7'
  },
  {
    region: 'Europe',
    phone: '+44 20 7911 LXGO',
    email: '<EMAIL>',
    hours: '24/7'
  },
  {
    region: 'Asia Pacific',
    phone: '+65 6911 LXGO',
    email: '<EMAIL>',
    hours: '24/7'
  }
];

const safetyTips = [
  {
    title: 'Before Your Experience',
    tips: [
      'Verify all booking details and confirmations',
      'Share your itinerary with trusted contacts',
      'Check local emergency numbers and embassy contacts',
      'Ensure you have appropriate insurance coverage'
    ]
  },
  {
    title: 'During Your Experience',
    tips: [
      'Keep emergency contacts readily available',
      'Stay in regular communication with our concierge team',
      'Trust your instincts and report any concerns immediately',
      'Keep important documents secure but accessible'
    ]
  },
  {
    title: 'In Case of Emergency',
    tips: [
      'Contact local emergency services first (911, 112, etc.)',
      'Immediately notify LXGO emergency hotline',
      'Follow instructions from local authorities',
      'Keep detailed records of any incidents'
    ]
  }
];

export default function SafetyPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-transparent to-secondary/5" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,oklch(0.55_0.18_180_/_0.08),transparent_50%)]" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="max-w-4xl mx-auto text-center"
            >
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-serif font-bold mb-6">
                <span className="block">Your Safety</span>
                <span className="text-luxury">Our Priority</span>
              </h1>
              <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12">
                Comprehensive safety measures and protocols to ensure your peace of mind 
                during every luxury experience.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Emergency Contact */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="glass rounded-2xl p-8 border border-red-500/20 bg-red-50/10 mb-16"
              >
                <div className="flex items-center gap-3 mb-6">
                  <AlertTriangle className="w-8 h-8 text-red-500" />
                  <h2 className="text-2xl font-serif font-bold">Emergency Assistance</h2>
                </div>
                <p className="text-muted-foreground mb-6">
                  In case of emergency, contact local emergency services first, then reach out to our 24/7 emergency support team.
                </p>
                <div className="grid md:grid-cols-3 gap-6">
                  {emergencyContacts.map((contact, index) => (
                    <div key={index} className="text-center">
                      <h3 className="font-semibold mb-2">{contact.region}</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center justify-center gap-2">
                          <Phone className="w-4 h-4 text-red-500" />
                          <span className="font-mono">{contact.phone}</span>
                        </div>
                        <div className="flex items-center justify-center gap-2">
                          <Mail className="w-4 h-4 text-red-500" />
                          <span>{contact.email}</span>
                        </div>
                        <div className="flex items-center justify-center gap-2">
                          <Clock className="w-4 h-4 text-red-500" />
                          <span>{contact.hours}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Safety Features */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Safety <span className="text-luxury">Features</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Multi-layered security measures designed to protect you at every step.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
              {safetyFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-8 border border-border/20 hover:border-primary/30 transition-all hover-lift"
                >
                  <div className="w-16 h-16 mb-6 rounded-full gradient-luxury flex items-center justify-center">
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-serif font-bold mb-4">{feature.title}</h3>
                  <p className="text-muted-foreground mb-6">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-center gap-3 text-sm">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        <span>{detail}</span>
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Safety Tips */}
        <section className="py-20 relative">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Safety <span className="text-luxury">Guidelines</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Essential safety tips to help you make the most of your luxury experiences.
              </p>
            </motion.div>

            <div className="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {safetyTips.map((section, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-8 border border-border/20"
                >
                  <h3 className="text-xl font-serif font-bold mb-6">{section.title}</h3>
                  <ul className="space-y-4">
                    {section.tips.map((tip, tipIndex) => (
                      <li key={tipIndex} className="flex items-start gap-3 text-sm">
                        <CheckCircle className="w-4 h-4 text-primary flex-shrink-0 mt-0.5" />
                        <span>{tip}</span>
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Additional Resources */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Additional <span className="text-luxury">Resources</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Helpful resources and information to enhance your safety awareness.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="glass rounded-2xl p-6 border border-border/20 hover:border-primary/30 transition-all hover-lift text-center"
              >
                <FileText className="w-12 h-12 mx-auto mb-4 text-primary" />
                <h3 className="text-lg font-serif font-bold mb-3">Safety Handbook</h3>
                <p className="text-muted-foreground text-sm mb-4">
                  Comprehensive guide to staying safe during luxury travel experiences.
                </p>
                <button className="px-4 py-2 rounded-full glass border border-primary/20 text-primary font-medium hover:border-primary/40 transition-colors">
                  Download PDF
                </button>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
                className="glass rounded-2xl p-6 border border-border/20 hover:border-primary/30 transition-all hover-lift text-center"
              >
                <Globe className="w-12 h-12 mx-auto mb-4 text-primary" />
                <h3 className="text-lg font-serif font-bold mb-3">Travel Advisories</h3>
                <p className="text-muted-foreground text-sm mb-4">
                  Up-to-date travel safety information for destinations worldwide.
                </p>
                <button className="px-4 py-2 rounded-full glass border border-primary/20 text-primary font-medium hover:border-primary/40 transition-colors">
                  View Updates
                </button>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="glass rounded-2xl p-6 border border-border/20 hover:border-primary/30 transition-all hover-lift text-center"
              >
                <Phone className="w-12 h-12 mx-auto mb-4 text-primary" />
                <h3 className="text-lg font-serif font-bold mb-3">Safety Hotline</h3>
                <p className="text-muted-foreground text-sm mb-4">
                  24/7 dedicated safety support line for immediate assistance.
                </p>
                <button className="px-4 py-2 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform">
                  Call Now
                </button>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-20 relative">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 gradient-luxury opacity-10" />
          </div>
          
          <div className="container mx-auto px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="max-w-3xl mx-auto"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Questions About <span className="text-luxury">Safety</span>?
              </h2>
              <p className="text-xl text-muted-foreground mb-8">
                Our safety team is here to address any concerns and provide additional guidance.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/contact"
                  className="px-8 py-4 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg"
                >
                  Contact Safety Team
                </Link>
                <Link
                  href="/help"
                  className="px-8 py-4 rounded-full glass border border-primary/20 text-foreground font-semibold hover:border-primary/40 transition-colors"
                >
                  View FAQ
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
