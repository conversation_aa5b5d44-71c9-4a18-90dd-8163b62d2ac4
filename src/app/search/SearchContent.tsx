'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Search, Filter, X, Star, MapPin, Clock, Users, Calendar } from 'lucide-react';
import Link from 'next/link';

// Mock search results
const searchResults = [
  {
    id: 1,
    title: 'Luxury Yacht Charter',
    location: 'Miami, FL',
    price: 3500,
    rating: 4.9,
    reviewCount: 124,
    duration: '8 hours',
    capacity: 'Up to 10 guests',
    image: 'https://images.unsplash.com/photo-1518540119877-991d5d3c5a0e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
  },
  // ... rest of your search results
];

function SearchResults() {
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    priceRange: [0, 10000],
    rating: 0,
    location: '',
  });

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {searchResults.map((item) => (
        <motion.div
          key={item.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
        >
          <div className="relative h-48">
            <img
              src={item.image}
              alt={item.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute top-2 right-2 bg-white/80 backdrop-blur-sm px-2 py-1 rounded-full text-sm font-medium flex items-center">
              <Star className="w-4 h-4 text-yellow-500 mr-1" />
              {item.rating}
            </div>
          </div>
          <div className="p-4">
            <h3 className="font-bold text-lg mb-1">{item.title}</h3>
            <p className="text-gray-600 text-sm mb-2 flex items-center">
              <MapPin className="w-4 h-4 mr-1" />
              {item.location}
            </p>
            <div className="flex justify-between items-center mt-4">
              <span className="text-xl font-bold">${item.price}</span>
              <Link
                href={`/listing/${item.id}`}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                View Details
              </Link>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}

export default function SearchContent() {
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto mb-8">
        <div className="relative">
          <input
            type="text"
            placeholder="Search for experiences, destinations, or activities..."
            className="w-full px-6 py-4 pr-12 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <button className="absolute right-2 top-1/2 -translate-y-1/2 bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors">
            <Search className="w-6 h-6" />
          </button>
        </div>

        <div className="mt-4 flex justify-between items-center">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center text-gray-700 hover:text-blue-600 transition-colors"
          >
            <Filter className="w-5 h-5 mr-2" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
          <div className="text-sm text-gray-500">
            {searchResults.length} results found
          </div>
        </div>

        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-4 p-6 bg-gray-50 rounded-xl overflow-hidden"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price Range
                </label>
                <div className="flex items-center space-x-4">
                  <input
                    type="range"
                    min="0"
                    max="10000"
                    step="100"
                    className="w-full"
                  />
                </div>
              </div>
              {/* Add more filter options as needed */}
            </div>
          </motion.div>
        )}
      </div>

      <Suspense fallback={
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      }>
        <SearchResults />
      </Suspense>
    </div>
  );
}
