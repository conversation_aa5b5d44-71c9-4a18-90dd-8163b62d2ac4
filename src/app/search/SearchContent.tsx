'use client';

import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Search, Filter, X, Star, MapPin, Clock, Users } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

// Mock search results
const searchResults = [
  {
    id: 1,
    title: 'Luxury Yacht Charter',
    location: 'Miami, FL',
    price: 3500,
    rating: 4.9,
    reviewCount: 124,
    duration: '8 hours',
    capacity: 'Up to 10 guests',
    image: 'https://images.unsplash.com/photo-1518540119877-991d5d3c5a0e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Experiences'
  },
  {
    id: 2,
    title: 'Beachfront Villa Stay',
    location: 'Malibu, CA',
    price: 2800,
    rating: 4.8,
    reviewCount: 89,
    duration: '3 nights',
    capacity: '6 guests',
    image: 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Stays'
  },
  {
    id: 3,
    title: 'Luxury Car Rental',
    location: 'Los Angeles, CA',
    price: 1200,
    rating: 4.7,
    reviewCount: 215,
    duration: '24 hours',
    capacity: '2 passengers',
    image: 'https://images.unsplash.com/photo-1503376785-2a5b6c8a0881?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Vehicles'
  },
  {
    id: 4,
    title: 'Private Jet Charter',
    location: 'New York, NY',
    price: 15000,
    rating: 4.9,
    reviewCount: 56,
    duration: '2 hours',
    capacity: '8 passengers',
    image: 'https://images.unsplash.com/photo-1556388155-5e0c74cb3c8d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Experiences'
  },
  {
    id: 5,
    title: 'Mountain Cabin Retreat',
    location: 'Aspen, CO',
    price: 3200,
    rating: 4.8,
    reviewCount: 142,
    duration: '7 nights',
    capacity: '8 guests',
    image: 'https://images.unsplash.com/photo-1480074568708-e7b720bb3f09?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Stays'
  },
  {
    id: 6,
    title: 'Superyacht Experience',
    location: 'Fort Lauderdale, FL',
    price: 25000,
    rating: 5.0,
    reviewCount: 32,
    duration: '24 hours',
    capacity: '12 guests',
    image: 'https://images.unsplash.com/photo-1505881502353-a1986add38fe?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Boats & Yachts'
  },
];

const categories = [
  { id: 'all', name: 'All Categories' },
  { id: 'stays', name: 'Stays' },
  { id: 'experiences', name: 'Experiences' },
  { id: 'vehicles', name: 'Vehicles' },
  { id: 'boats', name: 'Boats & Yachts' },
];

export default function SearchContent() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  const [searchQuery, setSearchQuery] = useState(query);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [filters, setFilters] = useState({
    priceRange: [0, 10000],
    rating: 0,
    sortBy: 'relevance',
  });
  const [showFilters, setShowFilters] = useState(false);

  // Filter results based on search query and filters
  const filteredResults = searchResults.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || 
                           item.category.toLowerCase().includes(selectedCategory);
    const matchesPrice = item.price >= filters.priceRange[0] && 
                        item.price <= filters.priceRange[1];
    const matchesRating = item.rating >= filters.rating;
    
    return matchesSearch && matchesCategory && matchesPrice && matchesRating;
  });

  // Sort results
  const sortedResults = [...filteredResults].sort((a, b) => {
    switch (filters.sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'rating':
        return b.rating - a.rating;
      default: // relevance
        return 0;
    }
  });

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
      {/* Search Header */}
      <div className="mb-8">
        <motion.h1 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-3xl md:text-4xl font-serif font-bold mb-2"
        >
          {query ? `Results for "${query}"` : 'Search Experiences'}
        </motion.h1>
        <p className="text-muted-foreground">
          {filteredResults.length} {filteredResults.length === 1 ? 'result' : 'results'} found
        </p>
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        {/* Filters Sidebar */}
        <div className="w-full md:w-64 flex-shrink-0">
          <div className="sticky top-24 space-y-6">
            <div className="bg-card rounded-xl p-6 border border-border/20">
              <h3 className="font-medium mb-4 flex items-center justify-between">
                <span>Filters</span>
                <button 
                  onClick={() => setShowFilters(!showFilters)}
                  className="md:hidden text-primary"
                >
                  {showFilters ? <X className="h-5 w-5" /> : <Filter className="h-5 w-5" />}
                </button>
              </h3>
              
              <div className={`space-y-6 ${!showFilters ? 'hidden md:block' : ''}`}>
                <div>
                  <h4 className="text-sm font-medium mb-2">Price Range</h4>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">${filters.priceRange[0]}</span>
                    <span className="text-sm text-muted-foreground">${filters.priceRange[1]}+</span>
                  </div>
                  <input
                    type="range"
                    min="0"
                    max="10000"
                    step="100"
                    value={filters.priceRange[1]}
                    onChange={(e) => setFilters({...filters, priceRange: [filters.priceRange[0], parseInt(e.target.value)]})}
                    className="w-full"
                  />
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">Minimum Rating</h4>
                  <div className="flex items-center space-x-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        onClick={() => setFilters({...filters, rating: star})}
                        className={`p-1 ${filters.rating >= star ? 'text-amber-400' : 'text-muted-foreground/30'}`}
                      >
                        <Star className="h-5 w-5 fill-current" />
                      </button>
                    ))}
                    {filters.rating > 0 && (
                      <button 
                        onClick={() => setFilters({...filters, rating: 0})}
                        className="ml-2 text-xs text-muted-foreground hover:text-foreground"
                      >
                        Clear
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {/* Search and Sort Bar */}
          <div className="bg-card rounded-xl p-4 mb-6 border border-border/20 flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="relative w-full sm:max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search experiences, locations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            
            <div className="flex items-center space-x-2 w-full sm:w-auto">
              <div className="relative">
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters({...filters, sortBy: e.target.value})}
                  className="appearance-none bg-background border border-border rounded-lg pl-4 pr-8 py-2 text-sm focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="relevance">Sort by: Relevance</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="rating">Highest Rated</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-muted-foreground">
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              
              <button 
                onClick={() => setShowFilters(!showFilters)}
                className="md:hidden p-2 text-muted-foreground hover:text-foreground"
              >
                <Filter className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Category Tabs */}
          <div className="flex overflow-x-auto pb-2 mb-6 -mx-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id === 'all' ? 'all' : category.name.toLowerCase())}
                className={`px-4 py-2 rounded-full whitespace-nowrap mx-1 text-sm font-medium transition-colors ${
                  (selectedCategory === 'all' && category.id === 'all') || 
                  (selectedCategory === category.name.toLowerCase())
                    ? 'bg-primary text-white'
                    : 'bg-muted/50 hover:bg-muted/70 text-foreground/80'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Search Results */}
          {sortedResults.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {sortedResults.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="group"
                >
                  <Link href={`/experience/${item.id}`}>
                    <div className="bg-card rounded-xl overflow-hidden border border-border/20 hover:shadow-md transition-shadow">
                      <div className="relative h-48 bg-muted/30">
                        <Image
                          src={item.image}
                          alt={item.title}
                          width={400}
                          height={192}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute top-3 left-3 bg-background/90 text-foreground text-xs font-medium px-2 py-1 rounded-full">
                          {item.category}
                        </div>
                      </div>
                      <div className="p-4">
                        <div className="flex justify-between items-start">
                          <h3 className="font-medium text-foreground group-hover:text-primary transition-colors line-clamp-1">
                            {item.title}
                          </h3>
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-amber-400 fill-current" />
                            <span className="ml-1 text-sm">{item.rating}</span>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1 flex items-center">
                          <MapPin className="h-3.5 w-3.5 mr-1" />
                          {item.location}
                        </p>
                        <div className="mt-3 flex items-center text-xs text-muted-foreground space-x-4">
                          <span className="flex items-center">
                            <Clock className="h-3.5 w-3.5 mr-1" />
                            {item.duration}
                          </span>
                          <span className="flex items-center">
                            <Users className="h-3.5 w-3.5 mr-1" />
                            {item.capacity}
                          </span>
                        </div>
                        <div className="mt-4 flex justify-between items-center">
                          <div>
                            <span className="text-lg font-semibold">${item.price}</span>
                            <span className="text-sm text-muted-foreground"> total</span>
                          </div>
                          <button className="text-sm bg-primary text-white px-3 py-1.5 rounded-full hover:bg-primary/90 transition-colors">
                            View Details
                          </button>
                        </div>
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-16 bg-card rounded-xl border border-dashed border-border/30">
              <Search className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-1">No results found</h3>
              <p className="text-muted-foreground mb-6">Try adjusting your search or filter criteria</p>
              <button
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('all');
                  setFilters({
                    priceRange: [0, 10000],
                    rating: 0,
                    sortBy: 'relevance',
                  });
                }}
                className="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-full shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                Clear Filters
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Search, Filter, X, Star, MapPin, Clock, Users, Calendar } from 'lucide-react';
import Link from 'next/link';

// Mock search results
const searchResults = [
  {
    id: 1,
    title: 'Luxury Yacht Charter',
    location: 'Miami, FL',
    price: 3500,
    rating: 4.9,
    reviewCount: 124,
    duration: '8 hours',
    capacity: 'Up to 10 guests',
    image: 'https://images.unsplash.com/photo-1518540119877-991d5d3c5a0e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
  },
  // ... rest of your search results
];

function SearchResults() {
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    priceRange: [0, 10000],
    rating: 0,
    location: '',
  });

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {searchResults.map((item) => (
        <motion.div
          key={item.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
        >
          <div className="relative h-48">
            <img
              src={item.image}
              alt={item.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute top-2 right-2 bg-white/80 backdrop-blur-sm px-2 py-1 rounded-full text-sm font-medium flex items-center">
              <Star className="w-4 h-4 text-yellow-500 mr-1" />
              {item.rating}
            </div>
          </div>
          <div className="p-4">
            <h3 className="font-bold text-lg mb-1">{item.title}</h3>
            <p className="text-gray-600 text-sm mb-2 flex items-center">
              <MapPin className="w-4 h-4 mr-1" />
              {item.location}
            </p>
            <div className="flex justify-between items-center mt-4">
              <span className="text-xl font-bold">${item.price}</span>
              <Link
                href={`/listing/${item.id}`}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                View Details
              </Link>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}

export default function SearchContent() {
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto mb-8">
        <div className="relative">
          <input
            type="text"
            placeholder="Search for experiences, destinations, or activities..."
            className="w-full px-6 py-4 pr-12 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <button className="absolute right-2 top-1/2 -translate-y-1/2 bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors">
            <Search className="w-6 h-6" />
          </button>
        </div>

        <div className="mt-4 flex justify-between items-center">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center text-gray-700 hover:text-blue-600 transition-colors"
          >
            <Filter className="w-5 h-5 mr-2" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
          <div className="text-sm text-gray-500">
            {searchResults.length} results found
          </div>
        </div>

        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-4 p-6 bg-gray-50 rounded-xl overflow-hidden"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price Range
                </label>
                <div className="flex items-center space-x-4">
                  <input
                    type="range"
                    min="0"
                    max="10000"
                    step="100"
                    className="w-full"
                  />
                </div>
              </div>
              {/* Add more filter options as needed */}
            </div>
          </motion.div>
        )}
      </div>

      <Suspense fallback={
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      }>
        <SearchResults />
      </Suspense>
    </div>
  );
}
