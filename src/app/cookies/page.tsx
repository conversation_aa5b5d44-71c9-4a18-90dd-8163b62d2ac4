'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  Settings, 
  Shield, 
  BarChart, 
  Target,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

const cookieTypes = [
  {
    name: 'Essential Cookies',
    icon: Shield,
    description: 'Required for the website to function properly. These cannot be disabled.',
    purpose: 'Authentication, security, basic functionality',
    retention: 'Session or up to 1 year',
    required: true,
    enabled: true
  },
  {
    name: 'Analytics Cookies',
    icon: Bar<PERSON>hart,
    description: 'Help us understand how visitors interact with our website.',
    purpose: 'Website performance analysis, user behavior tracking',
    retention: 'Up to 2 years',
    required: false,
    enabled: true
  },
  {
    name: 'Marketing Cookies',
    icon: Target,
    description: 'Used to deliver personalized advertisements and track campaign effectiveness.',
    purpose: 'Personalized advertising, campaign tracking',
    retention: 'Up to 1 year',
    required: false,
    enabled: false
  },
  {
    name: 'Preference Cookies',
    icon: Settings,
    description: 'Remember your preferences and settings for a better experience.',
    purpose: 'Language preferences, theme settings, personalization',
    retention: 'Up to 1 year',
    required: false,
    enabled: true
  }
];

export default function CookiesPage() {
  const [cookieSettings, setCookieSettings] = useState(
    cookieTypes.reduce((acc, cookie) => ({
      ...acc,
      [cookie.name]: cookie.enabled
    }), {})
  );

  const handleCookieToggle = (cookieName: string) => {
    const cookie = cookieTypes.find(c => c.name === cookieName);
    if (cookie && !cookie.required) {
      setCookieSettings(prev => ({
        ...prev,
        [cookieName]: !prev[cookieName]
      }));
    }
  };

  const handleSaveSettings = () => {
    // Save cookie preferences
    console.log('Cookie settings saved:', cookieSettings);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-transparent to-secondary/5" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,oklch(0.55_0.18_180_/_0.08),transparent_50%)]" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="max-w-4xl mx-auto text-center"
            >
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-serif font-bold mb-6">
                <span className="block">Cookie</span>
                <span className="text-luxury">Policy</span>
              </h1>
              <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12">
                Learn about how we use cookies to enhance your experience on LXGO Concierge.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Cookie Settings */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Cookie <span className="text-luxury">Preferences</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Customize your cookie preferences to control how we collect and use data.
              </p>
            </motion.div>

            <div className="max-w-4xl mx-auto space-y-6">
              {cookieTypes.map((cookie, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-8 border border-border/20"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start gap-4 flex-1">
                      <div className="w-12 h-12 rounded-full gradient-luxury flex items-center justify-center">
                        <cookie.icon className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-xl font-serif font-bold">{cookie.name}</h3>
                          {cookie.required && (
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-600">
                              Required
                            </span>
                          )}
                        </div>
                        <p className="text-muted-foreground mb-4">{cookie.description}</p>
                        <div className="grid md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Purpose:</span>
                            <p className="text-muted-foreground">{cookie.purpose}</p>
                          </div>
                          <div>
                            <span className="font-medium">Retention:</span>
                            <p className="text-muted-foreground">{cookie.retention}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="ml-6">
                      <button
                        onClick={() => handleCookieToggle(cookie.name)}
                        disabled={cookie.required}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          cookieSettings[cookie.name] 
                            ? 'bg-primary' 
                            : 'bg-gray-200'
                        } ${cookie.required ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            cookieSettings[cookie.name] ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            <div className="max-w-4xl mx-auto mt-8 flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={handleSaveSettings}
                className="px-8 py-4 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg"
              >
                Save Preferences
              </button>
              <button className="px-8 py-4 rounded-full glass border border-primary/20 text-foreground font-semibold hover:border-primary/40 transition-colors">
                Accept All
              </button>
              <button className="px-8 py-4 rounded-full glass border border-border/20 text-muted-foreground font-semibold hover:border-border/40 transition-colors">
                Reject Optional
              </button>
            </div>
          </div>
        </section>

        {/* What Are Cookies */}
        <section className="py-20 relative">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent" />
          </div>
          
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-16"
              >
                <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                  What Are <span className="text-luxury">Cookies</span>?
                </h2>
                <p className="text-xl text-muted-foreground">
                  Understanding how cookies work and why we use them.
                </p>
              </motion.div>

              <div className="space-y-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-8 border border-border/20"
                >
                  <div className="flex items-start gap-4">
                    <Cookie className="w-8 h-8 text-primary flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="text-xl font-serif font-bold mb-4">What Are Cookies?</h3>
                      <p className="text-muted-foreground mb-4">
                        Cookies are small text files that are stored on your device when you visit our website. 
                        They help us provide you with a better, more personalized experience by remembering 
                        your preferences and analyzing how you use our services.
                      </p>
                      <p className="text-muted-foreground">
                        Cookies cannot harm your device or files, and they don&apos;t contain personal information
                        unless you specifically provide it to us.
                      </p>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-8 border border-border/20"
                >
                  <div className="flex items-start gap-4">
                    <Info className="w-8 h-8 text-primary flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="text-xl font-serif font-bold mb-4">How We Use Cookies</h3>
                      <ul className="space-y-3 text-muted-foreground">
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                          <span>Remember your login status and preferences</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                          <span>Analyze website traffic and user behavior</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                          <span>Personalize content and advertisements</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                          <span>Improve our services and user experience</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                          <span>Ensure website security and prevent fraud</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-8 border border-border/20"
                >
                  <div className="flex items-start gap-4">
                    <Settings className="w-8 h-8 text-primary flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="text-xl font-serif font-bold mb-4">Managing Your Cookies</h3>
                      <p className="text-muted-foreground mb-4">
                        You have full control over your cookie preferences. You can:
                      </p>
                      <ul className="space-y-2 text-muted-foreground">
                        <li className="flex items-center gap-2">
                          <span className="w-1.5 h-1.5 rounded-full bg-primary"></span>
                          <span>Use our cookie preference center above to customize settings</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="w-1.5 h-1.5 rounded-full bg-primary"></span>
                          <span>Configure your browser settings to block or delete cookies</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="w-1.5 h-1.5 rounded-full bg-primary"></span>
                          <span>Opt out of targeted advertising through industry tools</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </section>

        {/* Third-Party Cookies */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-16"
              >
                <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                  Third-Party <span className="text-luxury">Services</span>
                </h2>
                <p className="text-xl text-muted-foreground">
                  We work with trusted partners to enhance your experience.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="glass rounded-2xl p-8 border border-border/20"
              >
                <h3 className="text-xl font-serif font-bold mb-6">Third-Party Cookie Partners</h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Analytics</h4>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Google Analytics - Website traffic analysis</li>
                      <li>• Hotjar - User behavior insights</li>
                      <li>• Mixpanel - Event tracking</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Marketing</h4>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Google Ads - Advertising campaigns</li>
                      <li>• Facebook Pixel - Social media advertising</li>
                      <li>• LinkedIn Insight - Professional targeting</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Functionality</h4>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Stripe - Payment processing</li>
                      <li>• Intercom - Customer support chat</li>
                      <li>• Calendly - Appointment scheduling</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Security</h4>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Cloudflare - DDoS protection</li>
                      <li>• reCAPTCHA - Spam prevention</li>
                      <li>• Auth0 - Authentication services</li>
                    </ul>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-20 relative">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 gradient-luxury opacity-10" />
          </div>
          
          <div className="container mx-auto px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="max-w-3xl mx-auto"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Questions About <span className="text-luxury">Cookies</span>?
              </h2>
              <p className="text-xl text-muted-foreground mb-8">
                If you have any questions about our cookie policy or how we use cookies, 
                please don&apos;t hesitate to contact us.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/contact"
                  className="px-8 py-4 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg"
                >
                  Contact Us
                </Link>
                <Link
                  href="/privacy"
                  className="px-8 py-4 rounded-full glass border border-primary/20 text-foreground font-semibold hover:border-primary/40 transition-colors"
                >
                  Privacy Policy
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
