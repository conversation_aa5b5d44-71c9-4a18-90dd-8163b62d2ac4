@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --font-serif: Source Serif 4, serif;
  --radius: 0.375rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-destructive-foreground: var(--destructive-foreground);
}

:root {
  --radius: 0.75rem;

  /* Luxury Color Palette: Light Teal and Amber */
  --background: oklch(0.98 0.008 180); /* Soft white with subtle teal hint */
  --foreground: oklch(0.15 0.02 240); /* Rich black */
  --card: oklch(0.99 0.005 180);
  --card-foreground: oklch(0.15 0.02 240);
  --popover: oklch(0.99 0.005 180);
  --popover-foreground: oklch(0.15 0.02 240);

  /* Primary: Luxurious Teal */
  --primary: oklch(0.55 0.18 180); /* Rich teal */
  --primary-foreground: oklch(0.98 0.008 180);

  /* Secondary: Warm Amber */
  --secondary: oklch(0.75 0.15 60); /* Warm amber */
  --secondary-foreground: oklch(0.15 0.02 240);

  /* Accent: Bright Amber */
  --accent: oklch(0.68 0.18 50); /* Vibrant amber */
  --accent-foreground: oklch(0.15 0.02 240);

  --muted: oklch(0.94 0.01 180);
  --muted-foreground: oklch(0.45 0.02 240);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(1.0000 0 0);

  /* Borders with subtle teal tint */
  --border: oklch(0.88 0.02 180 / 0.3);
  --input: oklch(0.92 0.02 180);
  --ring: oklch(0.55 0.18 180);

  /* Chart colors with luxury palette */
  --chart-1: oklch(0.55 0.18 180); /* Teal */
  --chart-2: oklch(0.68 0.18 50); /* Amber */
  --chart-3: oklch(0.45 0.15 200); /* Deep teal */
  --chart-4: oklch(0.78 0.12 45); /* Light amber */
  --chart-5: oklch(0.25 0.05 240); /* Charcoal */

  /* Sidebar */
  --sidebar: oklch(0.97 0.005 180);
  --sidebar-foreground: oklch(0.15 0.02 240);
  --sidebar-primary: oklch(0.55 0.18 180);
  --sidebar-primary-foreground: oklch(0.98 0.008 180);
  --sidebar-accent: oklch(0.68 0.18 50);
  --sidebar-accent-foreground: oklch(0.15 0.02 240);
  --sidebar-border: oklch(0.88 0.02 180 / 0.3);
  --sidebar-ring: oklch(0.55 0.18 180);

  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-serif: 'Playfair Display', 'Source Serif 4', serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* Enhanced shadows for luxury feel */
  --shadow-color: hsl(180 30% 10%);
  --shadow-opacity: 0.15;
  --shadow-blur: 16px;
  --shadow-spread: -4px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 8px;
  --letter-spacing: -0.01em;
  --spacing: 0.25rem;

  /* Luxury shadow system */
  --shadow-2xs: 0px 2px 4px -1px hsl(180 30% 10% / 0.08);
  --shadow-xs: 0px 4px 8px -2px hsl(180 30% 10% / 0.10);
  --shadow-sm: 0px 6px 12px -2px hsl(180 30% 10% / 0.12), 0px 2px 4px -2px hsl(180 30% 10% / 0.08);
  --shadow: 0px 8px 16px -4px hsl(180 30% 10% / 0.15), 0px 4px 8px -2px hsl(180 30% 10% / 0.10);
  --shadow-md: 0px 12px 24px -4px hsl(180 30% 10% / 0.18), 0px 6px 12px -2px hsl(180 30% 10% / 0.12);
  --shadow-lg: 0px 16px 32px -8px hsl(180 30% 10% / 0.20), 0px 8px 16px -4px hsl(180 30% 10% / 0.15);
  --shadow-xl: 0px 24px 48px -12px hsl(180 30% 10% / 0.25), 0px 12px 24px -6px hsl(180 30% 10% / 0.18);
  --shadow-2xl: 0px 32px 64px -16px hsl(180 30% 10% / 0.30);
  --tracking-normal: -0.01em;
}

.dark {
  /* Dark mode with luxury teal and amber accents */
  --background: oklch(0.08 0.02 240); /* Rich dark background */
  --foreground: oklch(0.95 0.005 180); /* Soft white with teal hint */
  --card: oklch(0.12 0.02 240);
  --card-foreground: oklch(0.95 0.005 180);
  --popover: oklch(0.12 0.02 240);
  --popover-foreground: oklch(0.95 0.005 180);

  /* Primary: Bright Teal for dark mode */
  --primary: oklch(0.65 0.20 180); /* Brighter teal for contrast */
  --primary-foreground: oklch(0.08 0.02 240);

  /* Secondary: Warm Amber */
  --secondary: oklch(0.78 0.18 55); /* Bright amber */
  --secondary-foreground: oklch(0.08 0.02 240);

  /* Accent: Vibrant Amber */
  --accent: oklch(0.72 0.20 50); /* Glowing amber */
  --accent-foreground: oklch(0.08 0.02 240);

  --muted: oklch(0.18 0.02 240);
  --muted-foreground: oklch(0.65 0.02 180);
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: oklch(1.0000 0 0);

  /* Borders with subtle glow */
  --border: oklch(0.25 0.03 180 / 0.4);
  --input: oklch(0.22 0.03 180 / 0.6);
  --ring: oklch(0.65 0.20 180);

  /* Chart colors for dark mode */
  --chart-1: oklch(0.65 0.20 180); /* Bright teal */
  --chart-2: oklch(0.78 0.18 55); /* Bright amber */
  --chart-3: oklch(0.55 0.18 200); /* Medium teal */
  --chart-4: oklch(0.85 0.15 50); /* Light amber */
  --chart-5: oklch(0.75 0.08 180); /* Light teal */

  /* Sidebar */
  --sidebar: oklch(0.10 0.02 240);
  --sidebar-foreground: oklch(0.95 0.005 180);
  --sidebar-primary: oklch(0.65 0.20 180);
  --sidebar-primary-foreground: oklch(0.08 0.02 240);
  --sidebar-accent: oklch(0.78 0.18 55);
  --sidebar-accent-foreground: oklch(0.08 0.02 240);
  --sidebar-border: oklch(0.25 0.03 180 / 0.4);
  --sidebar-ring: oklch(0.65 0.20 180);

  /* Enhanced shadows for dark mode */
  --shadow-color: hsl(240 50% 5%);
  --shadow-opacity: 0.30;
  --shadow-blur: 20px;
  --shadow-spread: -6px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 10px;

  /* Dark mode luxury shadows */
  --shadow-2xs: 0px 2px 4px -1px hsl(240 50% 5% / 0.20);
  --shadow-xs: 0px 4px 8px -2px hsl(240 50% 5% / 0.25);
  --shadow-sm: 0px 6px 12px -2px hsl(240 50% 5% / 0.30), 0px 2px 4px -2px hsl(240 50% 5% / 0.20);
  --shadow: 0px 8px 16px -4px hsl(240 50% 5% / 0.35), 0px 4px 8px -2px hsl(240 50% 5% / 0.25);
  --shadow-md: 0px 12px 24px -4px hsl(240 50% 5% / 0.40), 0px 6px 12px -2px hsl(240 50% 5% / 0.30);
  --shadow-lg: 0px 16px 32px -8px hsl(240 50% 5% / 0.45), 0px 8px 16px -4px hsl(240 50% 5% / 0.35);
  --shadow-xl: 0px 24px 48px -12px hsl(240 50% 5% / 0.50), 0px 12px 24px -6px hsl(240 50% 5% / 0.40);
  --shadow-2xl: 0px 32px 64px -16px hsl(240 50% 5% / 0.55);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Luxury gradient utilities */
  .gradient-teal {
    background: linear-gradient(135deg, oklch(0.55 0.18 180), oklch(0.65 0.20 180));
  }

  .gradient-amber {
    background: linear-gradient(135deg, oklch(0.68 0.18 50), oklch(0.78 0.18 55));
  }

  .gradient-luxury {
    background: linear-gradient(135deg,
      oklch(0.55 0.18 180) 0%,
      oklch(0.68 0.18 50) 50%,
      oklch(0.78 0.18 55) 100%
    );
  }

  .gradient-luxury-reverse {
    background: linear-gradient(135deg,
      oklch(0.78 0.18 55) 0%,
      oklch(0.68 0.18 50) 50%,
      oklch(0.55 0.18 180) 100%
    );
  }

  .gradient-luxury-radial {
    background: radial-gradient(circle at center,
      oklch(0.55 0.18 180) 0%,
      oklch(0.68 0.18 50) 50%,
      oklch(0.78 0.18 55) 100%
    );
  }

  /* Glass morphism effects */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-teal {
    background: oklch(0.55 0.18 180 / 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid oklch(0.55 0.18 180 / 0.2);
  }

  .glass-amber {
    background: oklch(0.68 0.18 50 / 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid oklch(0.68 0.18 50 / 0.2);
  }

  /* Luxury animations */
  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes glow {
    0%, 100% { box-shadow: 0 0 20px oklch(0.55 0.18 180 / 0.3); }
    50% { box-shadow: 0 0 40px oklch(0.55 0.18 180 / 0.6); }
  }

  @keyframes pulse-luxury {
    0%, 100% {
      box-shadow: 0 0 0 0 oklch(0.55 0.18 180 / 0.4);
      transform: scale(1);
    }
    50% {
      box-shadow: 0 0 0 10px oklch(0.55 0.18 180 / 0);
      transform: scale(1.05);
    }
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .animate-pulse-luxury {
    animation: pulse-luxury 2s ease-in-out infinite;
  }

  /* Luxury text effects */
  .text-luxury {
    background: linear-gradient(135deg,
      oklch(0.55 0.18 180),
      oklch(0.68 0.18 50),
      oklch(0.78 0.18 55)
    );
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .text-shadow-luxury {
    text-shadow: 0 2px 4px oklch(0.55 0.18 180 / 0.3);
  }

  /* Luxury hover effects */
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
  }

  .hover-glow:hover {
    box-shadow: 0 0 30px oklch(0.55 0.18 180 / 0.4);
  }

  /* Additional utility classes */
  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }

  .from-teal-200\/10 {
    --tw-gradient-from: rgb(153 246 228 / 0.1);
    --tw-gradient-to: rgb(153 246 228 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .from-amber-200\/10 {
    --tw-gradient-from: rgb(253 230 138 / 0.1);
    --tw-gradient-to: rgb(253 230 138 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .from-teal-200\/20 {
    --tw-gradient-from: rgb(153 246 228 / 0.2);
    --tw-gradient-to: rgb(153 246 228 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .from-amber-200\/20 {
    --tw-gradient-from: rgb(253 230 138 / 0.2);
    --tw-gradient-to: rgb(253 230 138 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }
}