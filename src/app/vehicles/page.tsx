import { Metadata } from 'next';
import dynamic from 'next/dynamic';

// Using dynamic import to handle client components
const VehiclesSection = dynamic(
  () => import('@/components/home/<USER>'),
  { ssr: true }
);

export const metadata: Metadata = {
  title: 'Luxury Vehicles | LXGO Concierge',
  description: 'Rent or charter premium vehicles from the most exclusive brands in the world.',
};

export default function VehiclesPage() {
  return (
    <div className="pt-24 pb-16 md:pt-28">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-4">
            Luxury Vehicles
          </h1>
          <p className="text-xl text-muted-foreground">
            Experience the pinnacle of automotive excellence with our curated collection.
          </p>
        </div>
        
        <VehiclesSection />
      </div>
    </div>
  );
}
