import { Inter } from 'next/font/google';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'LXGO Concierge',
  description: 'Premium luxury concierge services for the discerning client.',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} antialiased`}>
        <div className="relative min-h-screen">
          {/* Luxury background overlay */}
          <div className="fixed inset-0 -z-50">
            <div className="absolute inset-0 bg-gradient-to-br from-background via-background/95 to-background/90" />
            <div className="absolute inset-0 bg-gradient-to-br from-teal-50/20 via-transparent to-amber-50/15" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,oklch(0.55_0.18_180_/_0.06),transparent_50%)]" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_75%_75%,oklch(0.68_0.18_50_/_0.04),transparent_50%)]" />
          </div>

          <div className="flex min-h-screen flex-col relative z-10">
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
        </div>
      </body>
    </html>
  );
}
