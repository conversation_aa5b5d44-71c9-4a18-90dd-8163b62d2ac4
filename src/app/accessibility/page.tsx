'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  Eye, 
  Ear, 
  Hand, 
  Brain, 
  Monitor, 
  Keyboard,
  Mouse,
  Smartphone,
  CheckCircle,
  Mail,
  Phone,
  Settings
} from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

const accessibilityFeatures = [
  {
    category: 'Visual Accessibility',
    icon: Eye,
    color: 'text-blue-500',
    features: [
      'High contrast color schemes',
      'Scalable text and UI elements',
      'Screen reader compatibility',
      'Alternative text for images',
      'Focus indicators for navigation',
      'Color-blind friendly design'
    ]
  },
  {
    category: 'Hearing Accessibility',
    icon: Ear,
    color: 'text-green-500',
    features: [
      'Closed captions for videos',
      'Visual alerts and notifications',
      'Text alternatives for audio content',
      'Sign language interpretation options',
      'Adjustable audio controls',
      'Silent mode functionality'
    ]
  },
  {
    category: 'Motor Accessibility',
    icon: Hand,
    color: 'text-purple-500',
    features: [
      'Keyboard navigation support',
      'Voice control compatibility',
      'Large clickable areas',
      'Drag and drop alternatives',
      'Customizable input methods',
      'Reduced motion options'
    ]
  },
  {
    category: 'Cognitive Accessibility',
    icon: Brain,
    color: 'text-orange-500',
    features: [
      'Clear and simple language',
      'Consistent navigation patterns',
      'Progress indicators',
      'Error prevention and recovery',
      'Customizable interface complexity',
      'Memory aids and reminders'
    ]
  }
];

const assistiveTechnologies = [
  {
    name: 'Screen Readers',
    icon: Monitor,
    description: 'Compatible with JAWS, NVDA, VoiceOver, and TalkBack',
    support: 'Full Support'
  },
  {
    name: 'Keyboard Navigation',
    icon: Keyboard,
    description: 'Complete functionality without mouse interaction',
    support: 'Full Support'
  },
  {
    name: 'Voice Control',
    icon: Mouse,
    description: 'Works with Dragon NaturallySpeaking and Voice Control',
    support: 'Full Support'
  },
  {
    name: 'Mobile Accessibility',
    icon: Smartphone,
    description: 'Optimized for iOS and Android accessibility features',
    support: 'Full Support'
  }
];

const wcagCompliance = [
  {
    level: 'WCAG 2.1 Level A',
    status: 'Compliant',
    description: 'Basic accessibility requirements met'
  },
  {
    level: 'WCAG 2.1 Level AA',
    status: 'Compliant',
    description: 'Enhanced accessibility standards achieved'
  },
  {
    level: 'WCAG 2.1 Level AAA',
    status: 'Partial',
    description: 'Working towards full compliance'
  }
];

export default function AccessibilityPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-transparent to-secondary/5" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,oklch(0.55_0.18_180_/_0.08),transparent_50%)]" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="max-w-4xl mx-auto text-center"
            >
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-serif font-bold mb-6">
                <span className="block">Accessibility</span>
                <span className="text-luxury">Statement</span>
              </h1>
              <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12">
                LXGO Concierge is committed to ensuring digital accessibility for all users, 
                regardless of their abilities or disabilities.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Commitment Section */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="glass rounded-2xl p-8 border border-primary/20 mb-16"
              >
                <h2 className="text-2xl font-serif font-bold mb-6">Our Commitment</h2>
                <p className="text-muted-foreground mb-6">
                  We believe that luxury experiences should be accessible to everyone. Our commitment to 
                  accessibility ensures that all users can navigate, understand, and interact with our 
                  platform effectively, regardless of their physical or cognitive abilities.
                </p>
                <p className="text-muted-foreground">
                  We continuously work to improve the accessibility of our services and welcome feedback 
                  from our community to help us create a more inclusive experience for all.
                </p>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Accessibility Features */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Accessibility <span className="text-luxury">Features</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Comprehensive accessibility features designed to support diverse user needs.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
              {accessibilityFeatures.map((category, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-8 border border-border/20 hover:border-primary/30 transition-all"
                >
                  <div className="flex items-center gap-4 mb-6">
                    <div className={`w-12 h-12 rounded-full glass border border-border/20 flex items-center justify-center ${category.color}`}>
                      <category.icon className="w-6 h-6" />
                    </div>
                    <h3 className="text-xl font-serif font-bold">{category.category}</h3>
                  </div>
                  <ul className="space-y-3">
                    {category.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start gap-3 text-sm">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                        <span className="text-muted-foreground">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Assistive Technologies */}
        <section className="py-20 relative">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Assistive <span className="text-luxury">Technology</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Our platform is designed to work seamlessly with popular assistive technologies.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
              {assistiveTechnologies.map((tech, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-6 border border-border/20 text-center"
                >
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full gradient-luxury flex items-center justify-center">
                    <tech.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-serif font-bold mb-3">{tech.name}</h3>
                  <p className="text-muted-foreground text-sm mb-4">{tech.description}</p>
                  <span className="px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-600">
                    {tech.support}
                  </span>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* WCAG Compliance */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                WCAG <span className="text-luxury">Compliance</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                We follow Web Content Accessibility Guidelines (WCAG) 2.1 standards.
              </p>
            </motion.div>

            <div className="max-w-4xl mx-auto">
              <div className="space-y-4">
                {wcagCompliance.map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="glass rounded-xl p-6 border border-border/20 flex items-center justify-between"
                  >
                    <div>
                      <h3 className="text-lg font-serif font-bold mb-2">{item.level}</h3>
                      <p className="text-muted-foreground text-sm">{item.description}</p>
                    </div>
                    <span className={`px-4 py-2 rounded-full text-sm font-medium ${
                      item.status === 'Compliant' 
                        ? 'bg-green-100 text-green-600' 
                        : 'bg-yellow-100 text-yellow-600'
                    }`}>
                      {item.status}
                    </span>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Accessibility Tools */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Accessibility <span className="text-luxury">Tools</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Built-in tools to customize your experience.
              </p>
            </motion.div>

            <div className="max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="glass rounded-2xl p-8 border border-border/20"
              >
                <div className="flex items-center gap-4 mb-6">
                  <Settings className="w-8 h-8 text-primary" />
                  <h3 className="text-2xl font-serif font-bold">Accessibility Settings</h3>
                </div>
                <p className="text-muted-foreground mb-6">
                  Customize your experience with our built-in accessibility tools:
                </p>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Visual Settings</h4>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Increase text size up to 200%</li>
                      <li>• High contrast mode</li>
                      <li>• Reduce motion and animations</li>
                      <li>• Focus highlighting</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Navigation Settings</h4>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Keyboard shortcuts</li>
                      <li>• Skip navigation links</li>
                      <li>• Voice navigation support</li>
                      <li>• Simplified interface mode</li>
                    </ul>
                  </div>
                </div>
                <div className="mt-6">
                  <button className="px-6 py-3 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform">
                    Open Accessibility Settings
                  </button>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Feedback Section */}
        <section className="py-20 relative">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 gradient-luxury opacity-10" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Help Us <span className="text-luxury">Improve</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Your feedback is essential in helping us create a more accessible experience for everyone.
              </p>
            </motion.div>

            <div className="max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="glass rounded-2xl p-8 border border-border/20"
              >
                <h3 className="text-xl font-serif font-bold mb-6">Accessibility Feedback</h3>
                <p className="text-muted-foreground mb-6">
                  If you encounter any accessibility barriers or have suggestions for improvement, 
                  please contact our accessibility team:
                </p>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-primary" />
                    <div>
                      <p className="font-medium">Email</p>
                      <p className="text-sm text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-primary" />
                    <div>
                      <p className="font-medium">Phone</p>
                      <p className="text-sm text-muted-foreground">+1 (555) 123-ACCESS</p>
                    </div>
                  </div>
                </div>
                <div className="mt-6 flex flex-col sm:flex-row gap-4">
                  <Link
                    href="/contact"
                    className="px-6 py-3 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform text-center"
                  >
                    Contact Accessibility Team
                  </Link>
                  <Link
                    href="/report"
                    className="px-6 py-3 rounded-full glass border border-primary/20 text-foreground font-medium hover:border-primary/40 transition-colors text-center"
                  >
                    Report Accessibility Issue
                  </Link>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Updates Section */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="glass rounded-2xl p-8 border border-amber-500/20 bg-amber-50/10"
              >
                <h3 className="text-xl font-serif font-bold mb-4">Ongoing Improvements</h3>
                <p className="text-muted-foreground mb-4">
                  We are continuously working to improve the accessibility of our platform. 
                  This statement will be updated as we implement new features and enhancements.
                </p>
                <p className="text-sm text-muted-foreground">
                  Last updated: {new Date().toLocaleDateString()}
                </p>
              </motion.div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
