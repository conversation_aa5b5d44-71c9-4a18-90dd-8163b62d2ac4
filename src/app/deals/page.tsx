import { Metadata } from 'next';
import dynamic from 'next/dynamic';

// Using dynamic import to handle client components
const DealsSection = dynamic(
  () => import('@/components/home/<USER>'),
  { ssr: true }
);

export const metadata: Metadata = {
  title: 'Exclusive Deals | LXGO Concierge',
  description: 'Discover exclusive deals and offers on luxury experiences and services.',
};

export default function DealsPage() {
  return (
    <div className="pt-24 pb-16 md:pt-28">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-4">
            Exclusive Deals
          </h1>
          <p className="text-xl text-muted-foreground">
            Limited-time offers on premium experiences and luxury services.
          </p>
        </div>
        
        <DealsSection />
      </div>
    </div>
  );
}
