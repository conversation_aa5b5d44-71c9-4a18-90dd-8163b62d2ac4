'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import {
  User,
  Settings,
  Bell,
  CreditCard,
  Heart,
  Clock,
  HelpCircle,
  LogOut,
  Plus,
  Trash2,
  Edit,
  Check,
  Moon,
  Sun,

  X,
  AlertCircle
} from 'lucide-react';

type TabType = 'profile' | 'bookings' | 'wishlist' | 'payment' | 'notifications' | 'settings';

type NotificationType = {
  id: number;
  title: string;
  message: string;
  date: string;
  read: boolean;
  type: 'booking' | 'promotion' | 'system' | 'payment';
};

type PaymentMethod = {
  id: string;
  type: 'visa' | 'mastercard' | 'amex' | 'paypal';
  last4: string;
  expiry: string;
  name: string;
  isDefault: boolean;
};

export default function AccountPage() {
  const [activeTab, setActiveTab] = useState<TabType>('profile');
  const [user, setUser] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    membership: 'Elite',
    memberSince: '2023',
    avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    preferences: {
      theme: 'light',
      language: 'en',
      currency: 'USD',
      marketingEmails: true,
      smsNotifications: false,
      twoFactorAuth: false,
    },
  });

  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    {
      id: 'card_1',
      type: 'visa',
      last4: '4242',
      expiry: '12/25',
      name: 'Alex Johnson',
      isDefault: true,
    },
    {
      id: 'card_2',
      type: 'mastercard',
      last4: '5555',
      expiry: '06/26',
      name: 'Alex Johnson',
      isDefault: false,
    },
  ]);

  const [notifications, setNotifications] = useState<NotificationType[]>([
    {
      id: 1,
      title: 'Booking Confirmed',
      message: 'Your luxury yacht charter for June 15, 2024 has been confirmed.',
      date: '10 min ago',
      read: false,
      type: 'booking',
    },
    {
      id: 2,
      title: 'Exclusive Offer',
      message: '20% off your next booking. Use code LUX20 at checkout.',
      date: '2 hours ago',
      read: true,
      type: 'promotion',
    },
    {
      id: 3,
      title: 'Payment Processed',
      message: 'Your payment of $3,500.00 for the yacht charter has been processed.',
      date: '1 day ago',
      read: true,
      type: 'payment',
    },
    {
      id: 4,
      title: 'System Maintenance',
      message: 'Scheduled maintenance on June 20, 2024 from 2:00 AM to 5:00 AM EST.',
      date: '2 days ago',
      read: true,
      type: 'system',
    },
  ]);

  const [showAddCard, setShowAddCard] = useState(false);
  const [newCard, setNewCard] = useState({
    cardNumber: '',
    expiry: '',
    cvc: '',
    name: '',
    saveCard: true,
  });

  const [settings, setSettings] = useState({
    firstName: 'Alex',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [activeSettingTab, setActiveSettingTab] = useState<'profile' | 'security' | 'preferences' | 'notifications'>('profile');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Payment Methods Handlers
  const handleAddCard = (e: React.FormEvent) => {
    e.preventDefault();
    if (newCard.cardNumber.length < 16 || !newCard.expiry || !newCard.cvc || !newCard.name) {
      setError('Please fill in all card details');
      return;
    }
    
    const newPaymentMethod: PaymentMethod = {
      id: `card_${Date.now()}`,
      type: newCard.cardNumber.startsWith('4') ? 'visa' : 'mastercard',
      last4: newCard.cardNumber.slice(-4),
      expiry: newCard.expiry,
      name: newCard.name,
      isDefault: paymentMethods.length === 0,
    };
    
    setPaymentMethods([...paymentMethods, newPaymentMethod]);
    setShowAddCard(false);
    setNewCard({
      cardNumber: '',
      expiry: '',
      cvc: '',
      name: '',
      saveCard: true,
    });
    setSuccess('Payment method added successfully');
    setTimeout(() => setSuccess(''), 3000);
  };

  const handleSetDefault = (id: string) => {
    setPaymentMethods(
      paymentMethods.map((method) => ({
        ...method,
        isDefault: method.id === id,
      }))
    );
  };

  const handleDeleteCard = (id: string) => {
    if (confirm('Are you sure you want to remove this payment method?')) {
      setPaymentMethods(paymentMethods.filter((method) => method.id !== id));
      setSuccess('Payment method removed');
      setTimeout(() => setSuccess(''), 3000);
    }
  };

  // Notifications Handlers
  const markAsRead = (id: number) => {
    setNotifications(
      notifications.map((notification) =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(
      notifications.map((notification) => ({
        ...notification,
        read: true,
      }))
    );
  };

  const deleteNotification = (id: number) => {
    setNotifications(notifications.filter((n) => n.id !== id));
  };

  // Settings Handlers
  const handleSettingsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    if (name.includes('.')) {
      const [, child] = name.split('.');
      setUser(prev => ({
        ...prev,
        preferences: {
          ...prev.preferences,
          [child]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
        },
      }));
    } else {
      setSettings(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
      }));
    }
  };

  const handleSaveSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess('');
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update user data
      setUser(prev => ({
        ...prev,
        name: `${settings.firstName} ${settings.lastName}`,
        email: settings.email,
        phone: settings.phone,
      }));
      
      setSuccess('Settings saved successfully!');
    } catch (err) {
      setError('Failed to save settings. Please try again.');
    } finally {
      setIsLoading(false);
      setTimeout(() => setSuccess(''), 5000);
    }
  };

  const navigation = [
    { id: 'profile', icon: User, label: 'My Profile' },
    { id: 'bookings', icon: Clock, label: 'My Bookings' },
    { id: 'wishlist', icon: Heart, label: 'Wishlist' },
    { id: 'payment', icon: CreditCard, label: 'Payment Methods' },
    { id: 'notifications', icon: Bell, label: 'Notifications' },
    { id: 'settings', icon: Settings, label: 'Account Settings' },
  ];

  const upcomingBookings = [
    {
      id: 1,
      title: 'Luxury Yacht Charter',
      date: 'Jun 15, 2024',
      status: 'Confirmed',
      image: 'https://images.unsplash.com/photo-*************-991d5d3c5a0e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    },
    {
      id: 2,
      title: 'Beachfront Villa Stay',
      date: 'Jul 22, 2024',
      status: 'Confirmed',
      image: 'https://images.unsplash.com/photo-*************-78b9dba3b914?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <div className="space-y-6">
            <div className="bg-card rounded-xl p-6 shadow-sm border border-border/20">
              <h3 className="text-lg font-medium mb-6">Personal Information</h3>
              <div className="flex flex-col sm:flex-row gap-6">
                <div className="flex-shrink-0">
                  <div className="relative w-24 h-24 rounded-full overflow-hidden border-2 border-primary/20">
                    <Image
                      src={user.avatar}
                      alt={user.name}
                      fill
                      className="object-cover"
                    />
                    <button className="absolute bottom-0 right-0 bg-primary text-white p-1.5 rounded-full hover:bg-primary/90 transition-colors">
                      <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </button>
                  </div>
                </div>
                <div className="flex-1 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-muted-foreground mb-1">Full Name</label>
                    <p className="text-foreground">{user.name}</p>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-muted-foreground mb-1">Email Address</label>
                      <p className="text-foreground">{user.email}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-muted-foreground mb-1">Phone Number</label>
                      <p className="text-foreground">{user.phone}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-muted-foreground mb-1">Membership</label>
                      <div className="flex items-center">
                        <span className="px-2 py-1 bg-gradient-to-r from-amber-500 to-amber-600 text-white text-xs font-medium rounded-full">
                          {user.membership}
                        </span>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-muted-foreground mb-1">Member Since</label>
                      <p className="text-foreground">{user.memberSince}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-8 pt-6 border-t border-border/20">
                <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                  Edit Profile
                </button>
              </div>
            </div>

            <div className="bg-card rounded-xl p-6 shadow-sm border border-border/20">
              <h3 className="text-lg font-medium mb-6">Upcoming Bookings</h3>
              {upcomingBookings.length > 0 ? (
                <div className="space-y-4">
                  {upcomingBookings.map((booking) => (
                    <div key={booking.id} className="flex border-b border-border/20 pb-4 last:border-0 last:pb-0">
                      <div className="w-24 h-24 rounded-lg overflow-hidden flex-shrink-0">
                        <Image
                          src={booking.image}
                          alt={booking.title}
                          width={96}
                          height={96}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="ml-4 flex-1">
                        <h4 className="font-medium">{booking.title}</h4>
                        <p className="text-sm text-muted-foreground mt-1">{booking.date}</p>
                        <div className="mt-2">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {booking.status}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4 flex items-center">
                        <button className="text-sm font-medium text-primary hover:text-primary/80 transition-colors">
                          View Details
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">You don&apos;t have any upcoming bookings.</p>
                  <Link
                    href="/marketplace"
                    className="inline-block mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    Explore Experiences
                  </Link>
                </div>
              )}
            </div>
          </div>
        );
      case 'bookings':
        return (
          <div className="bg-card rounded-xl p-6 shadow-sm border border-border/20">
            <h3 className="text-lg font-medium mb-6">My Bookings</h3>
            {upcomingBookings.length > 0 ? (
              <div className="space-y-4">
                {upcomingBookings.map((booking) => (
                  <div key={booking.id} className="p-4 border border-border/20 rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex flex-col sm:flex-row">
                      <div className="w-full sm:w-32 h-32 rounded-lg overflow-hidden flex-shrink-0">
                        <Image
                          src={booking.image}
                          alt={booking.title}
                          width={128}
                          height={128}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="mt-4 sm:mt-0 sm:ml-6 flex-1">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between">
                          <h4 className="text-lg font-medium">{booking.title}</h4>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-2 sm:mt-0">
                            {booking.status}
                          </span>
                        </div>
                        <p className="text-muted-foreground mt-1">{booking.date}</p>
                        <div className="mt-4 flex flex-wrap gap-2">
                          <button className="px-3 py-1.5 text-sm font-medium rounded-lg bg-primary/10 text-primary hover:bg-primary/20 transition-colors">
                            View Details
                          </button>
                          <button className="px-3 py-1.5 text-sm font-medium rounded-lg border border-border hover:bg-muted/50 transition-colors">
                            Download Invoice
                          </button>
                          <button className="px-3 py-1.5 text-sm font-medium rounded-lg border border-border hover:bg-muted/50 transition-colors">
                            Cancel Booking
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-muted/50 mb-4">
                  <Clock className="h-8 w-8 text-muted-foreground" />
                </div>
                <h4 className="text-lg font-medium text-foreground mb-2">No Bookings Yet</h4>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  You haven't made any bookings yet. Start exploring our luxury experiences and make your first booking today.
                </p>
                <Link
                  href="/marketplace"
                  className="inline-flex items-center px-6 py-2.5 bg-primary text-white rounded-full font-medium hover:bg-primary/90 transition-colors"
                >
                  Browse Experiences
                </Link>
              </div>
            )}
          </div>
        );
      case 'wishlist':
        return (
          <div className="bg-card rounded-xl p-6 shadow-sm border border-border/20">
            <h3 className="text-lg font-medium mb-6">My Wishlist</h3>
            <div className="text-center py-12">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-muted/50 mb-4">
                <Heart className="h-8 w-8 text-muted-foreground" />
              </div>
              <h4 className="text-lg font-medium text-foreground mb-2">Your wishlist is empty</h4>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Save your favorite experiences to your wishlist to keep track of what you love.
              </p>
              <Link
                href="/marketplace"
                className="inline-flex items-center px-6 py-2.5 bg-primary text-white rounded-full font-medium hover:bg-primary/90 transition-colors"
              >
                Browse Experiences
              </Link>
            </div>
          </div>
        );

      case 'notifications':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Notifications</h3>
              <button 
                onClick={markAllAsRead}
                className="text-sm text-primary hover:text-primary/80 transition-colors"
              >
                Mark all as read
              </button>
            </div>

            <div className="space-y-3">
              {notifications.length > 0 ? (
                notifications.map((notification) => (
                  <div 
                    key={notification.id} 
                    className={`p-4 rounded-xl border ${
                      notification.read 
                        ? 'bg-card border-border/20' 
                        : 'bg-blue-50 border-blue-100'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <h4 className="font-medium">{notification.title}</h4>
                          {!notification.read && (
                            <span className="ml-2 inline-block h-2 w-2 rounded-full bg-blue-500"></span>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
                        <div className="flex items-center mt-2 text-xs text-muted-foreground">
                          <span>{notification.date}</span>
                          {!notification.read && (
                            <button 
                              onClick={() => markAsRead(notification.id)}
                              className="ml-3 text-primary hover:text-primary/80"
                            >
                              Mark as read
                            </button>
                          )}
                        </div>
                      </div>
                      <button 
                        onClick={() => deleteNotification(notification.id)}
                        className="text-muted-foreground hover:text-destructive p-1"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-12">
                  <Bell className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h4 className="text-lg font-medium text-foreground mb-1">No notifications</h4>
                  <p className="text-muted-foreground">You don&apos;t have any notifications yet.</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'payment':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Payment Methods</h3>
              <button 
                onClick={() => setShowAddCard(!showAddCard)}
                className="inline-flex items-center px-4 py-2 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary/90 transition-colors"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Payment Method
              </button>
            </div>

            {showAddCard && (
              <div className="bg-muted/30 rounded-xl p-6 mb-6 border border-border/20">
                <h4 className="font-medium mb-4">Add New Card</h4>
                <form onSubmit={handleAddCard} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-muted-foreground mb-1">Card Number</label>
                    <input
                      type="text"
                      placeholder="1234 5678 9012 3456"
                      value={newCard.cardNumber}
                      onChange={(e) => setNewCard({...newCard, cardNumber: e.target.value})}
                      className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-muted-foreground mb-1">Expiry Date</label>
                      <input
                        type="text"
                        placeholder="MM/YY"
                        value={newCard.expiry}
                        onChange={(e) => setNewCard({...newCard, expiry: e.target.value})}
                        className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-muted-foreground mb-1">CVC</label>
                      <input
                        type="text"
                        placeholder="123"
                        value={newCard.cvc}
                        onChange={(e) => setNewCard({...newCard, cvc: e.target.value})}
                        className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        required
                      />
                    </div>
                    <div className="flex items-end">
                      <button
                        type="submit"
                        className="w-full bg-primary text-white py-2 px-4 rounded-lg hover:bg-primary/90 transition-colors"
                      >
                        Save Card
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="saveCard"
                      checked={newCard.saveCard}
                      onChange={(e) => setNewCard({...newCard, saveCard: e.target.checked})}
                      className="h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary"
                    />
                    <label htmlFor="saveCard" className="ml-2 block text-sm text-muted-foreground">
                      Save this card for future payments
                    </label>
                  </div>
                </form>
              </div>
            )}

            <div className="space-y-4">
              {paymentMethods.length > 0 ? (
                paymentMethods.map((method) => (
                  <div key={method.id} className="flex items-center justify-between p-4 bg-muted/20 rounded-xl border border-border/20">
                    <div className="flex items-center">
                      <div className="h-10 w-16 bg-white rounded-lg flex items-center justify-center shadow-sm mr-4">
                        {method.type === 'visa' ? (
                          <span className="text-blue-600 font-bold text-lg">VISA</span>
                        ) : method.type === 'mastercard' ? (
                          <span className="text-orange-500 font-bold text-sm">MASTERCARD</span>
                        ) : method.type === 'amex' ? (
                          <span className="text-blue-500 font-bold text-sm">AMEX</span>
                        ) : (
                          <span className="text-blue-500 font-bold text-sm">PP</span>
                        )}
                      </div>
                      <div>
                        <p className="font-medium">
                          {method.type.toUpperCase()} •••• {method.last4}
                        </p>
                        <p className="text-sm text-muted-foreground">Expires {method.expiry}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {method.isDefault ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Default
                        </span>
                      ) : (
                        <button
                          onClick={() => handleSetDefault(method.id)}
                          className="text-sm text-primary hover:text-primary/80"
                        >
                          Set as default
                        </button>
                      )}
                      <button
                        onClick={() => handleDeleteCard(method.id)}
                        className="text-muted-foreground hover:text-destructive p-1"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <CreditCard className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h4 className="text-lg font-medium text-foreground mb-1">No payment methods</h4>
                  <p className="text-muted-foreground mb-6">Add a payment method to make booking easier</p>
                  <button
                    onClick={() => setShowAddCard(true)}
                    className="inline-flex items-center px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Payment Method
                  </button>
                </div>
              )}
            </div>
          </div>
        );

      case 'settings':
        return (
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
              <div>
                <h3 className="text-lg font-medium">Account Settings</h3>
                <p className="text-sm text-muted-foreground">Manage your account settings and preferences</p>
              </div>
              <button
                onClick={handleSaveSettings}
                disabled={isLoading}
                className="px-4 py-2 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-70 disabled:cursor-not-allowed self-start"
              >
                {isLoading ? 'Saving...' : 'Save Changes'}
              </button>
            </div>

            {error && (
              <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-5 w-5 text-red-500" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {success && (
              <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <Check className="h-5 w-5 text-green-500" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-green-700">{success}</p>
                  </div>
                </div>
              </div>
            )}

            <div className="flex flex-col md:flex-row gap-6">
              {/* Sidebar */}
              <div className="w-full md:w-56 flex-shrink-0">
                <div className="space-y-1">
                  <button
                    onClick={() => setActiveSettingTab('profile')}
                    className={`w-full text-left px-4 py-2.5 rounded-lg text-sm font-medium transition-colors ${
                      activeSettingTab === 'profile'
                        ? 'bg-primary/10 text-primary'
                        : 'text-muted-foreground hover:bg-muted/50'
                    }`}
                  >
                    Profile Information
                  </button>
                  <button
                    onClick={() => setActiveSettingTab('security')}
                    className={`w-full text-left px-4 py-2.5 rounded-lg text-sm font-medium transition-colors ${
                      activeSettingTab === 'security'
                        ? 'bg-primary/10 text-primary'
                        : 'text-muted-foreground hover:bg-muted/50'
                    }`}
                  >
                    Security
                  </button>
                  <button
                    onClick={() => setActiveSettingTab('preferences')}
                    className={`w-full text-left px-4 py-2.5 rounded-lg text-sm font-medium transition-colors ${
                      activeSettingTab === 'preferences'
                        ? 'bg-primary/10 text-primary'
                        : 'text-muted-foreground hover:bg-muted/50'
                    }`}
                  >
                    Preferences
                  </button>
                  <button
                    onClick={() => setActiveSettingTab('notifications')}
                    className={`w-full text-left px-4 py-2.5 rounded-lg text-sm font-medium transition-colors ${
                      activeSettingTab === 'notifications'
                        ? 'bg-primary/10 text-primary'
                        : 'text-muted-foreground hover:bg-muted/50'
                    }`}
                  >
                    Notification Settings
                  </button>
                </div>
              </div>

              {/* Main Content */}
              <div className="flex-1">
                {activeSettingTab === 'profile' && (
                  <div className="space-y-6">
                    <div className="bg-card rounded-xl p-6 border border-border/20">
                      <h4 className="font-medium mb-6">Profile Information</h4>
                      <div className="space-y-4">
                        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-6">
                          <div className="relative h-20 w-20 rounded-full overflow-hidden border-2 border-primary/20">
                            <Image
                              src={user.avatar}
                              alt={user.name}
                              fill
                              className="object-cover"
                            />
                            <button className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                              <Edit className="h-5 w-5 text-white" />
                            </button>
                          </div>
                          <div>
                            <button className="text-sm text-primary hover:text-primary/80 mb-1">
                              Change photo
                            </button>
                            <p className="text-xs text-muted-foreground">JPG, GIF or PNG. Max 2MB</p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-muted-foreground mb-1">
                              First Name
                            </label>
                            <input
                              type="text"
                              name="firstName"
                              value={settings.firstName}
                              onChange={handleSettingsChange}
                              className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-muted-foreground mb-1">
                              Last Name
                            </label>
                            <input
                              type="text"
                              name="lastName"
                              value={settings.lastName}
                              onChange={handleSettingsChange}
                              className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                            />
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-muted-foreground mb-1">
                            Email Address
                          </label>
                          <input
                            type="email"
                            name="email"
                            value={settings.email}
                            onChange={handleSettingsChange}
                            className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-muted-foreground mb-1">
                            Phone Number
                          </label>
                          <input
                            type="tel"
                            name="phone"
                            value={settings.phone}
                            onChange={handleSettingsChange}
                            className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeSettingTab === 'security' && (
                  <div className="space-y-6">
                    <div className="bg-card rounded-xl p-6 border border-border/20">
                      <h4 className="font-medium mb-6">Change Password</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-muted-foreground mb-1">
                            Current Password
                          </label>
                          <div className="relative">
                            <input
                              type="password"
                              name="currentPassword"
                              value={settings.currentPassword}
                              onChange={handleSettingsChange}
                              className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Enter current password"
                            />
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-muted-foreground mb-1">
                            New Password
                          </label>
                          <div className="relative">
                            <input
                              type="password"
                              name="newPassword"
                              value={settings.newPassword}
                              onChange={handleSettingsChange}
                              className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Enter new password"
                            />
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-muted-foreground mb-1">
                            Confirm New Password
                          </label>
                          <div className="relative">
                            <input
                              type="password"
                              name="confirmPassword"
                              value={settings.confirmPassword}
                              onChange={handleSettingsChange}
                              className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Confirm new password"
                            />
                          </div>
                        </div>
                        
                        <div className="pt-2">
                          <button
                            type="button"
                            className="px-4 py-2 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary/90 transition-colors"
                          >
                            Update Password
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-card rounded-xl p-6 border border-border/20">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Two-Factor Authentication</h4>
                          <p className="text-sm text-muted-foreground mt-1">
                            Add an extra layer of security to your account
                          </p>
                        </div>
                        <button
                          type="button"
                          className={`px-4 py-2 rounded-lg text-sm font-medium ${
                            user.preferences.twoFactorAuth
                              ? 'bg-green-100 text-green-800 hover:bg-green-200'
                              : 'bg-muted text-foreground hover:bg-muted/80'
                          }`}
                          onClick={() => {
                            setUser({
                              ...user,
                              preferences: {
                                ...user.preferences,
                                twoFactorAuth: !user.preferences.twoFactorAuth,
                              },
                            });
                          }}
                        >
                          {user.preferences.twoFactorAuth ? 'Enabled' : 'Enable'}
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {activeSettingTab === 'preferences' && (
                  <div className="space-y-6">
                    <div className="bg-card rounded-xl p-6 border border-border/20">
                      <h4 className="font-medium mb-6">Display Preferences</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">Theme</p>
                            <p className="text-sm text-muted-foreground mt-1">
                              Choose how LXGO looks to you
                            </p>
                          </div>
                          <div className="flex items-center space-x-2 bg-muted rounded-lg p-1">
                            <button
                              onClick={() => {
                                setUser({
                                  ...user,
                                  preferences: {
                                    ...user.preferences,
                                    theme: 'light',
                                  },
                                });
                              }}
                              className={`px-3 py-1.5 text-sm rounded-md ${
                                user.preferences.theme === 'light'
                                  ? 'bg-white shadow-sm text-foreground'
                                  : 'text-muted-foreground hover:bg-white/50'
                              }`}
                            >
                              <Sun className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => {
                                setUser({
                                  ...user,
                                  preferences: {
                                    ...user.preferences,
                                    theme: 'dark',
                                  },
                                });
                              }}
                              className={`px-3 py-1.5 text-sm rounded-md ${
                                user.preferences.theme === 'dark'
                                  ? 'bg-gray-800 text-white shadow-sm'
                                  : 'text-muted-foreground hover:bg-gray-800/50'
                              }`}
                            >
                              <Moon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                        
                        <div className="pt-4 border-t border-border/20">
                          <label className="block text-sm font-medium text-muted-foreground mb-2">
                            Language
                          </label>
                          <select
                            name="language"
                            value={user.preferences.language}
                            onChange={handleSettingsChange}
                            className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          >
                            <option value="en">English</option>
                            <option value="es">Español</option>
                            <option value="fr">Français</option>
                            <option value="de">Deutsch</option>
                          </select>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-muted-foreground mb-2">
                            Currency
                          </label>
                          <select
                            name="currency"
                            value={user.preferences.currency}
                            onChange={handleSettingsChange}
                            className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          >
                            <option value="USD">USD - US Dollar</option>
                            <option value="EUR">EUR - Euro</option>
                            <option value="GBP">GBP - British Pound</option>
                            <option value="JPY">JPY - Japanese Yen</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeSettingTab === 'notifications' && (
                  <div className="space-y-6">
                    <div className="bg-card rounded-xl p-6 border border-border/20">
                      <h4 className="font-medium mb-6">Email Notifications</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">Marketing Emails</p>
                            <p className="text-sm text-muted-foreground">
                              Receive updates about new features and promotions
                            </p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              name="marketingEmails"
                              checked={user.preferences.marketingEmails}
                              onChange={handleSettingsChange}
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                          </label>
                        </div>
                        
                        <div className="pt-4 border-t border-border/20">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium">SMS Notifications</p>
                              <p className="text-sm text-muted-foreground">
                                Receive important updates via SMS
                              </p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                name="smsNotifications"
                                checked={user.preferences.smsNotifications}
                                onChange={handleSettingsChange}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-card rounded-xl p-6 border border-border/20">
                      <h4 className="font-medium mb-6">Notification Preferences</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">Booking Confirmations</p>
                            <p className="text-sm text-muted-foreground">
                              Get notified when your booking is confirmed
                            </p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              defaultChecked
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                          </label>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">Special Offers</p>
                            <p className="text-sm text-muted-foreground">
                              Receive exclusive deals and promotions
                            </p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              defaultChecked
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                          </label>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">Account Activity</p>
                            <p className="text-sm text-muted-foreground">
                              Get notified about important account changes
                            </p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              defaultChecked
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="bg-card rounded-xl p-6 shadow-sm border border-border/20">
            <h3 className="text-lg font-medium mb-6">
              {navigation.find((item) => item.id === activeTab)?.label}
            </h3>
            <p className="text-muted-foreground">
              This section is under development. Please check back later.
            </p>
          </div>
        );
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <h1 className="text-3xl md:text-4xl font-serif font-bold mb-2">My Account</h1>
        <p className="text-muted-foreground">Manage your profile, bookings, and preferences</p>
      </motion.div>

      <div className="flex flex-col md:flex-row gap-6">
        {/* Sidebar Navigation */}
        <div className="w-full md:w-64 flex-shrink-0">
          <div className="bg-card rounded-xl p-4 shadow-sm border border-border/20 sticky top-6">
            <div className="flex items-center space-x-3 p-3 rounded-lg bg-muted/30 mb-4">
              <div className="relative h-10 w-10 rounded-full overflow-hidden">
                <Image
                  src={user.avatar}
                  alt={user.name}
                  fill
                  className="object-cover"
                />
              </div>
              <div>
                <p className="font-medium text-sm">{user.name}</p>
                <p className="text-xs text-muted-foreground">{user.email}</p>
              </div>
            </div>
            
            <nav className="space-y-1">
              {navigation.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id as TabType)}
                  className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                    activeTab === item.id
                      ? 'bg-primary/10 text-primary'
                      : 'text-muted-foreground hover:bg-muted/50'
                  }`}
                >
                  <item.icon className="h-5 w-5 mr-3" />
                  {item.label}
                </button>
              ))}
              
              <button className="w-full flex items-center px-4 py-3 text-sm font-medium text-muted-foreground rounded-lg hover:bg-muted/50 transition-colors">
                <LogOut className="h-5 w-5 mr-3" />
                Sign Out
              </button>
            </nav>
            
            <div className="mt-6 pt-4 border-t border-border/20">
              <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
                <span>Membership</span>
                <span className="font-medium text-foreground">{user.membership}</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div className="bg-gradient-to-r from-amber-400 to-amber-600 h-2 rounded-full w-3/4"></div>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                3,500 points to Elite+ status
              </p>
            </div>
            
            <div className="mt-4 pt-4 border-t border-border/20">
              <Link
                href="/help"
                className="flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                <HelpCircle className="h-4 w-4 mr-2" />
                Help Center
              </Link>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
}
