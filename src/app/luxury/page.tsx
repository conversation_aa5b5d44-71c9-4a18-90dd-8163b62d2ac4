import { Metadata } from 'next';
import dynamic from 'next/dynamic';

// Using dynamic import to handle client components
const LuxurySection = dynamic(
  () => import('@/components/home/<USER>'),
  { ssr: true }
);

export const metadata: Metadata = {
  title: 'Ultimate Luxury | LXGO Concierge',
  description: 'Experience the epitome of luxury with our most exclusive offerings.',
};

export default function LuxuryPage() {
  return (
    <div className="pt-24 pb-16 md:pt-28">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-4">
            Ultimate Luxury
          </h1>
          <p className="text-xl text-muted-foreground">
            Indulge in our most exclusive and luxurious experiences.
          </p>
        </div>
        
        <LuxurySection />
      </div>
    </div>
  );
}
