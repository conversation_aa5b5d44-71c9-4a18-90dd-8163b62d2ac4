import { Metadata } from 'next';
import dynamic from 'next/dynamic';

// Using dynamic import to handle client components
const StaysSection = dynamic(
  () => import('@/components/home/<USER>'),
  { ssr: true }
);

export const metadata: Metadata = {
  title: 'Luxury Stays | LXGO Concierge',
  description: 'Discover and book the most exclusive luxury accommodations around the world.',
};

export default function StaysPage() {
  return (
    <div className="pt-24 pb-16 md:pt-28">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-4">
            Luxury Stays
          </h1>
          <p className="text-xl text-muted-foreground">
            Experience unparalleled comfort in the world's most exquisite accommodations.
          </p>
        </div>
        
        <StaysSection />
      </div>
    </div>
  );
}
