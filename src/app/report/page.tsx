'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { useState } from 'react';
import { 
  AlertTriangle, 
  Shield, 
  FileText, 
  Send,
  Phone,
  Mail,
  Clock,
  CheckCircle,
  User,
  Calendar,
  MapPin,
  Camera
} from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

const reportTypes = [
  {
    type: 'Safety Concern',
    description: 'Report safety issues or concerns about services or partners',
    icon: Shield,
    color: 'text-red-500',
    urgent: true
  },
  {
    type: 'Service Quality',
    description: 'Report issues with service quality or unmet expectations',
    icon: FileText,
    color: 'text-orange-500',
    urgent: false
  },
  {
    type: 'Billing Issue',
    description: 'Report problems with charges, refunds, or payment processing',
    icon: FileText,
    color: 'text-blue-500',
    urgent: false
  },
  {
    type: 'Partner Misconduct',
    description: 'Report inappropriate behavior by service partners',
    icon: AlertTriangle,
    color: 'text-red-500',
    urgent: true
  },
  {
    type: 'Platform Issue',
    description: 'Report technical problems or bugs with the LXGO platform',
    icon: FileText,
    color: 'text-purple-500',
    urgent: false
  },
  {
    type: 'Other',
    description: 'Report any other concerns or feedback',
    icon: FileText,
    color: 'text-gray-500',
    urgent: false
  }
];

export default function ReportPage() {
  const [selectedType, setSelectedType] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    bookingId: '',
    date: '',
    location: '',
    description: '',
    urgent: false
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Report submitted:', { type: selectedType, ...formData });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    });
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-transparent to-secondary/5" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,oklch(0.55_0.18_180_/_0.08),transparent_50%)]" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="max-w-4xl mx-auto text-center"
            >
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-serif font-bold mb-6">
                <span className="block">Report an</span>
                <span className="text-luxury">Issue</span>
              </h1>
              <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12">
                Help us maintain the highest standards by reporting any concerns 
                or issues with our services.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Emergency Contact */}
        <section className="py-10 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="glass rounded-2xl p-6 border border-red-500/20 bg-red-50/10 mb-16"
              >
                <div className="flex items-center gap-3 mb-4">
                  <AlertTriangle className="w-6 h-6 text-red-500" />
                  <h2 className="text-xl font-serif font-bold">Urgent Safety Concerns</h2>
                </div>
                <p className="text-muted-foreground mb-4">
                  If you're experiencing an immediate safety concern or emergency, please contact local authorities first, 
                  then reach out to our emergency hotline.
                </p>
                <div className="flex flex-col sm:flex-row gap-3">
                  <button className="px-6 py-3 rounded-full bg-red-500 text-white font-medium hover:bg-red-600 transition-colors flex items-center gap-2">
                    <Phone className="w-4 h-4" />
                    Emergency: +1 (555) 911-LXGO
                  </button>
                  <button className="px-6 py-3 rounded-full glass border border-red-500/20 text-red-600 font-medium hover:border-red-500/40 transition-colors flex items-center gap-2">
                    <Mail className="w-4 h-4" />
                    <EMAIL>
                  </button>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Report Types */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                What Would You Like to <span className="text-luxury">Report</span>?
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Select the type of issue you'd like to report to help us route your concern appropriately.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto mb-16">
              {reportTypes.map((type, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className={`glass rounded-2xl p-6 border cursor-pointer transition-all hover-lift ${
                    selectedType === type.type 
                      ? 'border-primary/40 bg-primary/5' 
                      : 'border-border/20 hover:border-primary/30'
                  }`}
                  onClick={() => setSelectedType(type.type)}
                >
                  <div className="flex items-start gap-4">
                    <div className={`w-12 h-12 rounded-full glass border border-border/20 flex items-center justify-center ${type.color}`}>
                      <type.icon className="w-6 h-6" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-serif font-bold">{type.type}</h3>
                        {type.urgent && (
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-600">
                            Urgent
                          </span>
                        )}
                      </div>
                      <p className="text-muted-foreground text-sm">{type.description}</p>
                    </div>
                    {selectedType === type.type && (
                      <CheckCircle className="w-5 h-5 text-primary" />
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Report Form */}
        {selectedType && (
          <section className="py-20 relative">
            <div className="absolute inset-0 -z-10">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent" />
            </div>
            
            <div className="container mx-auto px-6">
              <div className="max-w-4xl mx-auto">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-8 border border-border/20"
                >
                  <h2 className="text-2xl font-serif font-bold mb-6">
                    Report: {selectedType}
                  </h2>
                  
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Contact Information */}
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium mb-2">Full Name *</label>
                        <div className="relative">
                          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                          <input
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleChange}
                            className="w-full pl-10 pr-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                            placeholder="Your full name"
                            required
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-2">Email Address *</label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                          <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleChange}
                            className="w-full pl-10 pr-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                            placeholder="<EMAIL>"
                            required
                          />
                        </div>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium mb-2">Phone Number</label>
                        <div className="relative">
                          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                          <input
                            type="tel"
                            name="phone"
                            value={formData.phone}
                            onChange={handleChange}
                            className="w-full pl-10 pr-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                            placeholder="+****************"
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-2">Booking ID (if applicable)</label>
                        <input
                          type="text"
                          name="bookingId"
                          value={formData.bookingId}
                          onChange={handleChange}
                          className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                          placeholder="LXGO-123456"
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium mb-2">Date of Incident</label>
                        <div className="relative">
                          <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                          <input
                            type="date"
                            name="date"
                            value={formData.date}
                            onChange={handleChange}
                            className="w-full pl-10 pr-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-2">Location</label>
                        <div className="relative">
                          <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                          <input
                            type="text"
                            name="location"
                            value={formData.location}
                            onChange={handleChange}
                            className="w-full pl-10 pr-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                            placeholder="City, Country"
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">Detailed Description *</label>
                      <textarea
                        name="description"
                        value={formData.description}
                        onChange={handleChange}
                        rows={6}
                        className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors resize-none"
                        placeholder="Please provide as much detail as possible about the issue..."
                        required
                      />
                    </div>

                    <div className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        name="urgent"
                        checked={formData.urgent}
                        onChange={handleChange}
                        className="w-4 h-4 rounded border-border/20 text-primary focus:ring-primary/20"
                      />
                      <label className="text-sm text-muted-foreground">
                        This is an urgent matter requiring immediate attention
                      </label>
                    </div>

                    <div className="glass rounded-xl p-4 border border-border/20">
                      <div className="flex items-start gap-3">
                        <Camera className="w-5 h-5 text-muted-foreground mt-1" />
                        <div>
                          <h4 className="font-medium mb-2">Attach Supporting Documents</h4>
                          <p className="text-sm text-muted-foreground mb-3">
                            You can attach photos, screenshots, or documents to support your report.
                          </p>
                          <button
                            type="button"
                            className="px-4 py-2 rounded-full glass border border-primary/20 text-primary font-medium hover:border-primary/40 transition-colors text-sm"
                          >
                            Choose Files
                          </button>
                        </div>
                      </div>
                    </div>

                    <button
                      type="submit"
                      className="w-full px-8 py-4 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg flex items-center justify-center gap-2"
                    >
                      Submit Report
                      <Send className="w-5 h-5" />
                    </button>
                  </form>
                </motion.div>
              </div>
            </div>
          </section>
        )}

        {/* What Happens Next */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                What Happens <span className="text-luxury">Next</span>?
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Here's what you can expect after submitting your report.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-16 h-16 mx-auto mb-4 rounded-full gradient-luxury flex items-center justify-center text-white text-xl font-bold">
                  1
                </div>
                <h3 className="text-lg font-serif font-bold mb-3">Immediate Acknowledgment</h3>
                <p className="text-muted-foreground text-sm">
                  You'll receive an email confirmation with your report ID within minutes.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-16 h-16 mx-auto mb-4 rounded-full gradient-luxury flex items-center justify-center text-white text-xl font-bold">
                  2
                </div>
                <h3 className="text-lg font-serif font-bold mb-3">Investigation</h3>
                <p className="text-muted-foreground text-sm">
                  Our team will investigate your report and may contact you for additional information.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-16 h-16 mx-auto mb-4 rounded-full gradient-luxury flex items-center justify-center text-white text-xl font-bold">
                  3
                </div>
                <h3 className="text-lg font-serif font-bold mb-3">Resolution</h3>
                <p className="text-muted-foreground text-sm">
                  We'll take appropriate action and keep you informed of the outcome.
                </p>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-20 relative">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 gradient-luxury opacity-10" />
          </div>
          
          <div className="container mx-auto px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="max-w-3xl mx-auto"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Need <span className="text-luxury">Assistance</span>?
              </h2>
              <p className="text-xl text-muted-foreground mb-8">
                Our support team is here to help you through the reporting process.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/contact"
                  className="px-8 py-4 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg"
                >
                  Contact Support
                </Link>
                <Link
                  href="/help"
                  className="px-8 py-4 rounded-full glass border border-primary/20 text-foreground font-semibold hover:border-primary/40 transition-colors"
                >
                  View FAQ
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
