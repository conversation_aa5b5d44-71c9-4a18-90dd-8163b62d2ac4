'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { 
  Calendar, 
  ExternalLink, 
  Download, 
  Mail,
  Phone,
  FileText,
  Award,
  Users
} from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

const pressReleases = [
  {
    title: 'LXGO Concierge Expands to 50 New Global Destinations',
    date: '2024-01-15',
    excerpt: 'Leading luxury concierge service announces major expansion across Europe, Asia, and the Americas.',
    category: 'Expansion'
  },
  {
    title: 'Partnership with Michelin-Starred Restaurants Worldwide',
    date: '2023-12-08',
    excerpt: 'Exclusive dining experiences now available through LXGO&apos;s premium concierge platform.',
    category: 'Partnership'
  },
  {
    title: 'LXGO Wins "Best Luxury Service Platform" Award',
    date: '2023-11-22',
    excerpt: 'Recognition for excellence in luxury lifestyle services and customer satisfaction.',
    category: 'Award'
  },
  {
    title: 'AI-Powered Concierge Service Launches',
    date: '2023-10-10',
    excerpt: 'Revolutionary AI technology enhances personalized luxury experience recommendations.',
    category: 'Innovation'
  }
];

const mediaKit = [
  {
    title: 'Company Logo Pack',
    description: 'High-resolution logos in various formats',
    type: 'ZIP',
    size: '2.4 MB'
  },
  {
    title: 'Brand Guidelines',
    description: 'Complete brand identity and usage guidelines',
    type: 'PDF',
    size: '1.8 MB'
  },
  {
    title: 'Executive Photos',
    description: 'Professional headshots of leadership team',
    type: 'ZIP',
    size: '5.2 MB'
  },
  {
    title: 'Company Fact Sheet',
    description: 'Key statistics and company information',
    type: 'PDF',
    size: '0.5 MB'
  }
];

const mediaLogos = [
  { name: 'Forbes', logo: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=200&h=100&fit=crop' },
  { name: 'Wall Street Journal', logo: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=200&h=100&fit=crop' },
  { name: 'Bloomberg', logo: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=200&h=100&fit=crop' },
  { name: 'Financial Times', logo: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=200&h=100&fit=crop' }
];

export default function PressPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-transparent to-secondary/5" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,oklch(0.55_0.18_180_/_0.08),transparent_50%)]" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="max-w-4xl mx-auto text-center"
            >
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-serif font-bold mb-6">
                <span className="block">Press &</span>
                <span className="text-luxury">Media Center</span>
              </h1>
              <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12">
                Latest news, press releases, and media resources from LXGO Concierge.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Press Contact */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="glass rounded-2xl p-8 border border-primary/20 mb-16"
              >
                <h2 className="text-2xl font-serif font-bold mb-6">Media Inquiries</h2>
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="font-semibold mb-4">Press Contact</h3>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <Mail className="w-5 h-5 text-primary" />
                        <span><EMAIL></span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Phone className="w-5 h-5 text-primary" />
                        <span>+1 (555) 123-PRESS</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-4">Media Kit</h3>
                    <p className="text-muted-foreground text-sm">
                      Download our complete media kit including logos, brand guidelines, 
                      and executive photos for your stories.
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Press Releases */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Latest <span className="text-luxury">News</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Stay updated with our latest announcements and company milestones.
              </p>
            </motion.div>

            <div className="max-w-4xl mx-auto space-y-6">
              {pressReleases.map((release, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-8 border border-border/20 hover:border-primary/30 transition-all hover-lift group"
                >
                  <div className="flex flex-col md:flex-row md:items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <span className="px-3 py-1 rounded-full text-xs font-medium glass-teal text-primary">
                          {release.category}
                        </span>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Calendar className="w-4 h-4" />
                          <span>{new Date(release.date).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <h3 className="text-xl font-semibold mb-3 font-serif">{release.title}</h3>
                      <p className="text-muted-foreground">{release.excerpt}</p>
                    </div>
                    <button className="mt-4 md:mt-0 md:ml-6 px-4 py-2 rounded-full glass border border-primary/20 text-primary font-medium hover:border-primary/40 transition-colors flex items-center gap-2">
                      Read More
                      <ExternalLink className="w-4 h-4" />
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Media Kit Downloads */}
        <section className="py-20 relative">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Media <span className="text-luxury">Resources</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Download high-quality assets and brand materials for your coverage.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
              {mediaKit.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-6 border border-border/20 hover:border-primary/30 transition-all hover-lift text-center group"
                >
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full gradient-luxury flex items-center justify-center">
                    <FileText className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="font-semibold mb-2 font-serif">{item.title}</h3>
                  <p className="text-sm text-muted-foreground mb-4">{item.description}</p>
                  <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                    <span>{item.type}</span>
                    <span>{item.size}</span>
                  </div>
                  <button className="w-full px-4 py-2 rounded-full glass border border-primary/20 text-primary font-medium hover:border-primary/40 transition-colors flex items-center justify-center gap-2">
                    <Download className="w-4 h-4" />
                    Download
                  </button>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Featured In */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Featured <span className="text-luxury">In</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Trusted by leading media outlets worldwide.
              </p>
            </motion.div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              {mediaLogos.map((media, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="glass rounded-xl p-6 border border-border/20 hover:border-primary/30 transition-all hover-lift flex items-center justify-center"
                >
                  <span className="text-lg font-semibold text-muted-foreground">{media.name}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
