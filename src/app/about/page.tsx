'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { Award, Users, Globe, Heart, Star, Shield, Sparkles } from 'lucide-react';
import Link from 'next/link';

const stats = [
  { value: '50K+', label: 'Premium Members', icon: Users },
  { value: '10K+', label: 'Luxury Partners', icon: Award },
  { value: '100+', label: 'Global Destinations', icon: Globe },
  { value: '24/7', label: 'Concierge Support', icon: Heart },
];

const values = [
  {
    icon: Star,
    title: 'Excellence',
    description: 'We pursue perfection in every detail, ensuring that each experience exceeds expectations.',
  },
  {
    icon: Shield,
    title: 'Trust',
    description: 'Your privacy and security are paramount. We maintain the highest standards of confidentiality.',
  },
  {
    icon: Sparkles,
    title: 'Innovation',
    description: 'We continuously evolve, leveraging cutting-edge technology to enhance your luxury experience.',
  },
  {
    icon: Heart,
    title: 'Personalization',
    description: 'Every service is tailored to your unique preferences and lifestyle requirements.',
  },
];

const team = [
  {
    name: '<PERSON>',
    role: 'Founder & CEO',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    bio: 'Former luxury hospitality executive with 15+ years of experience in curating exceptional experiences.',
  },
  {
    name: '<PERSON> <PERSON>',
    role: 'Chief Technology Officer',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    bio: 'AI and machine learning expert, pioneering the future of personalized luxury services.',
  },
  {
    name: 'Isabella Rodriguez',
    role: 'Head of Concierge Services',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
    bio: 'Master sommelier and luxury travel specialist with connections to the world\'s most exclusive venues.',
  },
];

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="fixed top-0 left-0 w-full z-50 glass border-b border-border/20">
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center gap-3">
            <Image src="/logo.png" alt="LXGO Logo" width={40} height={40} className="drop-shadow-lg" />
            <span className="text-xl font-serif font-bold text-luxury">LXGO Concierge</span>
          </Link>
          <nav className="hidden md:flex items-center gap-8">
            <Link href="/" className="text-foreground/80 hover:text-primary transition-colors">Home</Link>
            <Link href="/services" className="text-foreground/80 hover:text-primary transition-colors">Services</Link>
            <Link href="/contact" className="text-foreground/80 hover:text-primary transition-colors">Contact</Link>
            <Link href="/auth/login" className="px-6 py-2 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform">
              Sign In
            </Link>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-32 pb-20 relative overflow-hidden">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-transparent to-secondary/5" />
          <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-[0.02]" />
        </div>
        
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto text-center"
          >
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-serif font-bold mb-6">
              <span className="block">Redefining</span>
              <span className="text-luxury">Luxury Living</span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12">
              Since 2018, LXGO Concierge has been the trusted partner for discerning individuals 
              seeking extraordinary experiences and unparalleled service.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 relative">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className="w-16 h-16 mx-auto mb-4 rounded-full gradient-luxury flex items-center justify-center group-hover:scale-110 transition-transform">
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-3xl font-bold text-luxury mb-2">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20 relative">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                  Our <span className="text-luxury">Story</span>
                </h2>
                <div className="space-y-6 text-lg text-muted-foreground">
                  <p>
                    Born from a vision to democratize luxury, LXGO Concierge was founded by a team of 
                    hospitality veterans who recognized the need for a more personalized, technology-driven 
                    approach to luxury services.
                  </p>
                  <p>
                    What started as a boutique concierge service in New York has evolved into a global 
                    platform connecting discerning clients with the world's most exclusive experiences, 
                    from private island getaways to Michelin-starred dining reservations.
                  </p>
                  <p>
                    Today, we leverage cutting-edge AI and machine learning to anticipate your needs, 
                    while maintaining the human touch that makes each experience truly exceptional.
                  </p>
                </div>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="relative"
              >
                <div className="aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl">
                  <Image
                    src="https://images.unsplash.com/photo-1600298881974-6be191ceeda1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                    alt="Luxury concierge service"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="absolute -bottom-6 -right-6 w-32 h-32 gradient-luxury rounded-full opacity-20 animate-float"></div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 relative">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent" />
        </div>
        
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
              Our <span className="text-luxury">Values</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              The principles that guide every interaction and shape every experience we create.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className="w-20 h-20 mx-auto mb-6 rounded-full glass border border-primary/20 flex items-center justify-center group-hover:border-primary/40 transition-colors">
                  <value.icon className="w-10 h-10 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-4">{value.title}</h3>
                <p className="text-muted-foreground">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 relative">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
              Meet Our <span className="text-luxury">Team</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              The visionaries and experts behind your extraordinary experiences.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {team.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className="relative w-48 h-48 mx-auto mb-6 rounded-full overflow-hidden shadow-xl group-hover:shadow-2xl transition-shadow">
                  <Image
                    src={member.image}
                    alt={member.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                </div>
                <h3 className="text-xl font-semibold mb-2">{member.name}</h3>
                <p className="text-primary font-medium mb-4">{member.role}</p>
                <p className="text-muted-foreground text-sm">{member.bio}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 gradient-luxury opacity-10" />
        </div>
        
        <div className="container mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
              Ready to Experience <span className="text-luxury">Luxury</span>?
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              Join thousands of discerning individuals who trust LXGO Concierge 
              to curate their most memorable experiences.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/auth/signup"
                className="px-8 py-4 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg"
              >
                Start Your Journey
              </Link>
              <Link
                href="/contact"
                className="px-8 py-4 rounded-full glass border border-primary/20 text-foreground font-semibold hover:border-primary/40 transition-colors"
              >
                Contact Us
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
