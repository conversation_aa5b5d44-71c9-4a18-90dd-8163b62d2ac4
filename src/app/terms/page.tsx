'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="glass border-b border-border/20 sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center gap-3">
            <Image src="/logo.png" alt="LXGO Logo" width={40} height={40} className="drop-shadow-lg" />
            <span className="text-xl font-serif font-bold text-luxury">LXGO Concierge</span>
          </Link>
          <nav className="hidden md:flex items-center gap-8">
            <Link href="/" className="text-foreground/80 hover:text-primary transition-colors">Home</Link>
            <Link href="/about" className="text-foreground/80 hover:text-primary transition-colors">About</Link>
            <Link href="/contact" className="text-foreground/80 hover:text-primary transition-colors">Contact</Link>
          </nav>
        </div>
      </header>

      <div className="container mx-auto px-6 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          <h1 className="text-4xl md:text-5xl font-serif font-bold mb-6">
            Terms of <span className="text-luxury">Service</span>
          </h1>
          <p className="text-muted-foreground text-lg mb-8">
            Last updated: {new Date().toLocaleDateString()}
          </p>

          <div className="prose prose-lg max-w-none">
            <div className="glass rounded-xl p-8 border border-border/20 mb-8">
              <h2 className="text-2xl font-semibold mb-4">Welcome to LXGO Concierge</h2>
              <p className="text-muted-foreground">
                These Terms of Service govern your use of LXGO Concierge services. By accessing or using 
                our platform, you agree to be bound by these terms. Please read them carefully.
              </p>
            </div>

            <div className="space-y-8">
              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Service Description</h2>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    LXGO Concierge provides luxury lifestyle and concierge services, including but not limited to:
                  </p>
                  <div className="space-y-2">
                    <p>• Private aviation and luxury transportation arrangements</p>
                    <p>• Premium accommodation bookings</p>
                    <p>• Fine dining reservations and culinary experiences</p>
                    <p>• Event planning and management</p>
                    <p>• Personal shopping and lifestyle services</p>
                    <p>• 24/7 concierge support and assistance</p>
                  </div>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Account Registration</h2>
                <div className="space-y-4 text-muted-foreground">
                  <p>To use our services, you must:</p>
                  <div className="space-y-2">
                    <p>• Be at least 18 years old</p>
                    <p>• Provide accurate and complete information</p>
                    <p>• Maintain the security of your account credentials</p>
                    <p>• Notify us immediately of any unauthorized access</p>
                    <p>• Accept responsibility for all activities under your account</p>
                  </div>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Booking and Payment Terms</h2>
                <div className="space-y-4 text-muted-foreground">
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Booking Process</h3>
                    <p>All bookings are subject to availability and confirmation by our concierge team and service partners.</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Payment</h3>
                    <p>Payment is required at the time of booking confirmation. We accept major credit cards and wire transfers.</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Pricing</h3>
                    <p>All prices are subject to change and may include additional fees, taxes, and service charges.</p>
                  </div>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Cancellation Policy</h2>
                <div className="space-y-4 text-muted-foreground">
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Standard Cancellations</h3>
                    <p>Cancellations made 24+ hours in advance are eligible for full refund, minus processing fees.</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Late Cancellations</h3>
                    <p>Cancellations within 24 hours may incur penalties depending on the service provider's terms.</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">No-Shows</h3>
                    <p>No-shows will result in full charges with no refund available.</p>
                  </div>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">User Responsibilities</h2>
                <div className="space-y-4 text-muted-foreground">
                  <p>You agree to:</p>
                  <div className="space-y-2">
                    <p>• Use services in accordance with applicable laws</p>
                    <p>• Treat service providers and staff with respect</p>
                    <p>• Provide accurate information for bookings</p>
                    <p>• Comply with venue rules and regulations</p>
                    <p>• Report any issues promptly to our concierge team</p>
                    <p>• Not engage in fraudulent or abusive behavior</p>
                  </div>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Limitation of Liability</h2>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    LXGO Concierge acts as an intermediary between clients and service providers. While we strive 
                    for excellence, we cannot guarantee the performance of third-party providers.
                  </p>
                  <p>
                    Our liability is limited to the amount paid for the specific service in question. We are not 
                    liable for indirect, incidental, or consequential damages.
                  </p>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Intellectual Property</h2>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    All content, trademarks, and intellectual property on our platform are owned by LXGO Concierge 
                    or our licensors. You may not use, reproduce, or distribute our content without permission.
                  </p>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Dispute Resolution</h2>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    Any disputes will be resolved through binding arbitration in New York, NY, under the rules 
                    of the American Arbitration Association.
                  </p>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Changes to Terms</h2>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    We reserve the right to modify these terms at any time. Changes will be effective immediately 
                    upon posting. Continued use of our services constitutes acceptance of modified terms.
                  </p>
                </div>
              </section>

              <section className="glass rounded-xl p-8 border border-border/20">
                <h2 className="text-2xl font-semibold mb-4">Contact Information</h2>
                <div className="text-muted-foreground space-y-4">
                  <p>
                    For questions about these Terms of Service, please contact us:
                  </p>
                  <div className="space-y-2">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Phone:</strong> +1 (555) 123-LXGO</p>
                    <p><strong>Address:</strong> 432 Park Avenue, Suite 1200, New York, NY 10016</p>
                  </div>
                </div>
              </section>
            </div>

            <div className="mt-12 text-center">
              <Link
                href="/"
                className="px-8 py-3 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg"
              >
                Return to Home
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
