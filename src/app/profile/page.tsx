'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Settings,
  Bell,
  CreditCard,
  Shield,
  Star,
  Edit,
  Save,
  X,
  Camera
} from 'lucide-react';

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Park Avenue, New York, NY 10016',
    dateOfBirth: '1985-06-15',
    preferences: {
      cuisine: 'French, Italian, Japanese',
      accommodation: 'Five-star hotels, Private villas',
      transportation: 'Private jets, Luxury cars',
      activities: 'Wine tasting, Art galleries, Spa treatments'
    }
  });

  const handleSave = () => {
    // Handle save logic
    setIsEditing(false);
  };

  const handleChange = (field: string, value: string) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePreferenceChange = (field: string, value: string) => {
    setProfileData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [field]: value
      }
    }));
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="glass border-b border-border/20 sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center gap-3">
              <Image src="/logo.png" alt="LXGO Logo" width={40} height={40} className="drop-shadow-lg" />
              <span className="text-xl font-serif font-bold text-luxury">LXGO Concierge</span>
            </Link>
            
            <div className="flex items-center gap-4">
              <Link href="/dashboard" className="text-foreground/80 hover:text-primary transition-colors">
                Dashboard
              </Link>
              <button className="p-2 rounded-full glass border border-border/20 hover:border-primary/30 transition-colors">
                <Bell className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex items-center justify-between mb-8"
          >
            <div>
              <h1 className="text-3xl md:text-4xl font-serif font-bold mb-2">
                My <span className="text-luxury">Profile</span>
              </h1>
              <p className="text-muted-foreground">
                Manage your personal information and preferences
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              {isEditing ? (
                <>
                  <button
                    onClick={() => setIsEditing(false)}
                    className="px-4 py-2 rounded-full glass border border-border/20 text-foreground font-medium hover:border-destructive/30 transition-colors flex items-center gap-2"
                  >
                    <X className="w-4 h-4" />
                    Cancel
                  </button>
                  <button
                    onClick={handleSave}
                    className="px-4 py-2 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform flex items-center gap-2"
                  >
                    <Save className="w-4 h-4" />
                    Save Changes
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setIsEditing(true)}
                  className="px-4 py-2 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform flex items-center gap-2"
                >
                  <Edit className="w-4 h-4" />
                  Edit Profile
                </button>
              )}
            </div>
          </motion.div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Profile Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="lg:col-span-1"
            >
              <div className="glass rounded-xl p-8 border border-border/20 text-center">
                <div className="relative w-32 h-32 mx-auto mb-6">
                  <div className="w-full h-full rounded-full gradient-luxury flex items-center justify-center text-white text-4xl font-bold">
                    {profileData.firstName[0]}{profileData.lastName[0]}
                  </div>
                  {isEditing && (
                    <button className="absolute bottom-2 right-2 w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center hover:scale-110 transition-transform">
                      <Camera className="w-4 h-4" />
                    </button>
                  )}
                </div>
                
                <h2 className="text-2xl font-semibold mb-2">
                  {profileData.firstName} {profileData.lastName}
                </h2>
                <p className="text-muted-foreground mb-4">Premium Member</p>
                
                <div className="flex items-center justify-center gap-2 mb-6">
                  <Star className="w-4 h-4 text-secondary fill-current" />
                  <span className="text-sm font-medium">Platinum Status</span>
                </div>
                
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Member Since</span>
                    <span className="font-medium">2023</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Total Bookings</span>
                    <span className="font-medium">24</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Loyalty Points</span>
                    <span className="font-medium">12,450</span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Profile Details */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="lg:col-span-2 space-y-6"
            >
              {/* Personal Information */}
              <div className="glass rounded-xl p-8 border border-border/20">
                <h3 className="text-xl font-semibold mb-6">Personal Information</h3>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">First Name</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={profileData.firstName}
                        onChange={(e) => handleChange('firstName', e.target.value)}
                        className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                      />
                    ) : (
                      <div className="px-4 py-3 rounded-xl glass border border-border/20">
                        {profileData.firstName}
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Last Name</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={profileData.lastName}
                        onChange={(e) => handleChange('lastName', e.target.value)}
                        className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                      />
                    ) : (
                      <div className="px-4 py-3 rounded-xl glass border border-border/20">
                        {profileData.lastName}
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Email</label>
                    {isEditing ? (
                      <input
                        type="email"
                        value={profileData.email}
                        onChange={(e) => handleChange('email', e.target.value)}
                        className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                      />
                    ) : (
                      <div className="px-4 py-3 rounded-xl glass border border-border/20 flex items-center gap-3">
                        <Mail className="w-4 h-4 text-muted-foreground" />
                        {profileData.email}
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Phone</label>
                    {isEditing ? (
                      <input
                        type="tel"
                        value={profileData.phone}
                        onChange={(e) => handleChange('phone', e.target.value)}
                        className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                      />
                    ) : (
                      <div className="px-4 py-3 rounded-xl glass border border-border/20 flex items-center gap-3">
                        <Phone className="w-4 h-4 text-muted-foreground" />
                        {profileData.phone}
                      </div>
                    )}
                  </div>
                  
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium mb-2">Address</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={profileData.address}
                        onChange={(e) => handleChange('address', e.target.value)}
                        className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                      />
                    ) : (
                      <div className="px-4 py-3 rounded-xl glass border border-border/20 flex items-center gap-3">
                        <MapPin className="w-4 h-4 text-muted-foreground" />
                        {profileData.address}
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Date of Birth</label>
                    {isEditing ? (
                      <input
                        type="date"
                        value={profileData.dateOfBirth}
                        onChange={(e) => handleChange('dateOfBirth', e.target.value)}
                        className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                      />
                    ) : (
                      <div className="px-4 py-3 rounded-xl glass border border-border/20 flex items-center gap-3">
                        <Calendar className="w-4 h-4 text-muted-foreground" />
                        {new Date(profileData.dateOfBirth).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Preferences */}
              <div className="glass rounded-xl p-8 border border-border/20">
                <h3 className="text-xl font-semibold mb-6">Preferences</h3>
                
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">Preferred Cuisine</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={profileData.preferences.cuisine}
                        onChange={(e) => handlePreferenceChange('cuisine', e.target.value)}
                        className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                        placeholder="e.g., French, Italian, Japanese"
                      />
                    ) : (
                      <div className="px-4 py-3 rounded-xl glass border border-border/20">
                        {profileData.preferences.cuisine}
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Accommodation Preferences</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={profileData.preferences.accommodation}
                        onChange={(e) => handlePreferenceChange('accommodation', e.target.value)}
                        className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                        placeholder="e.g., Five-star hotels, Private villas"
                      />
                    ) : (
                      <div className="px-4 py-3 rounded-xl glass border border-border/20">
                        {profileData.preferences.accommodation}
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Transportation Preferences</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={profileData.preferences.transportation}
                        onChange={(e) => handlePreferenceChange('transportation', e.target.value)}
                        className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                        placeholder="e.g., Private jets, Luxury cars"
                      />
                    ) : (
                      <div className="px-4 py-3 rounded-xl glass border border-border/20">
                        {profileData.preferences.transportation}
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Preferred Activities</label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={profileData.preferences.activities}
                        onChange={(e) => handlePreferenceChange('activities', e.target.value)}
                        className="w-full px-4 py-3 rounded-xl glass border border-border/20 focus:border-primary/40 focus:outline-none transition-colors"
                        placeholder="e.g., Wine tasting, Art galleries, Spa treatments"
                      />
                    ) : (
                      <div className="px-4 py-3 rounded-xl glass border border-border/20">
                        {profileData.preferences.activities}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="grid md:grid-cols-3 gap-4">
                <Link
                  href="/settings"
                  className="glass rounded-xl p-6 border border-border/20 hover:border-primary/30 transition-all duration-300 hover:scale-105 text-center group"
                >
                  <Settings className="w-8 h-8 mx-auto mb-3 text-primary" />
                  <span className="font-medium">Account Settings</span>
                </Link>
                
                <Link
                  href="/billing"
                  className="glass rounded-xl p-6 border border-border/20 hover:border-primary/30 transition-all duration-300 hover:scale-105 text-center group"
                >
                  <CreditCard className="w-8 h-8 mx-auto mb-3 text-primary" />
                  <span className="font-medium">Billing & Payment</span>
                </Link>
                
                <Link
                  href="/security"
                  className="glass rounded-xl p-6 border border-border/20 hover:border-primary/30 transition-all duration-300 hover:scale-105 text-center group"
                >
                  <Shield className="w-8 h-8 mx-auto mb-3 text-primary" />
                  <span className="font-medium">Security</span>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
