'use client';

import { motion } from 'framer-motion';
import { ShoppingBag, X, Plus, Minus } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

// Mock cart data
const cartItems = [
  {
    id: 1,
    name: 'Luxury Yacht Charter',
    description: '8-hour private yacht rental for up to 10 guests',
    price: 3500,
    quantity: 1,
    image: 'https://images.unsplash.com/photo-1518540119877-991d5d3c5a0e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
  },
  {
    id: 2,
    name: 'Premium Villa Stay',
    description: '3 nights in a luxury beachfront villa',
    price: 2800,
    quantity: 1,
    image: 'https://images.unsplash.com/photo-1580587771525-78b9dba3b914?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
  },
];

export default function CartPage() {
  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const tax = subtotal * 0.1; // 10% tax
  const total = subtotal + tax;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-12"
      >
        <h1 className="text-3xl md:text-4xl font-serif font-bold mb-4">Your Cart</h1>
        <p className="text-muted-foreground">Review and manage your selected experiences</p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          {cartItems.length > 0 ? (
            <div className="bg-card rounded-xl shadow-sm border border-border/20 overflow-hidden">
              {cartItems.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex flex-col sm:flex-row border-b border-border/20 last:border-0 p-6"
                >
                  <div className="w-full sm:w-40 h-40 bg-muted/30 rounded-lg overflow-hidden mb-4 sm:mb-0 sm:mr-6">
                    <Image
                      src={item.image}
                      alt={item.name}
                      width={160}
                      height={160}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium text-lg">{item.name}</h3>
                        <p className="text-muted-foreground text-sm mt-1">{item.description}</p>
                      </div>
                      <button className="text-muted-foreground hover:text-destructive transition-colors">
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                    
                    <div className="mt-4 flex items-center justify-between">
                      <div className="flex items-center border border-border rounded-full overflow-hidden">
                        <button className="px-3 py-1 hover:bg-muted/50 transition-colors">
                          <Minus className="h-4 w-4" />
                        </button>
                        <span className="px-4">{item.quantity}</span>
                        <button className="px-3 py-1 hover:bg-muted/50 transition-colors">
                          <Plus className="h-4 w-4" />
                        </button>
                      </div>
                      <div className="text-lg font-medium">
                        ${(item.price * item.quantity).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-16 bg-card rounded-xl border border-dashed border-border/30">
              <ShoppingBag className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-1">Your cart is empty</h3>
              <p className="text-muted-foreground mb-6">Start adding experiences to your cart</p>
              <Link
                href="/marketplace"
                className="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-full shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                Browse Experiences
              </Link>
            </div>
          )}
        </div>

        {cartItems.length > 0 && (
          <div>
            <div className="bg-card rounded-xl shadow-sm border border-border/20 p-6 sticky top-6">
              <h3 className="text-lg font-medium mb-4">Order Summary</h3>
              
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Subtotal</span>
                  <span>${subtotal.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Tax (10%)</span>
                  <span>${tax.toLocaleString()}</span>
                </div>
                <div className="border-t border-border/20 pt-4 mt-4 flex justify-between text-lg font-medium">
                  <span>Total</span>
                  <span>${total.toLocaleString()}</span>
                </div>
              </div>

              <button
                className="w-full mt-8 bg-primary hover:bg-primary/90 text-white py-3 px-4 rounded-full font-medium transition-colors"
              >
                Proceed to Checkout
              </button>

              <p className="text-xs text-muted-foreground mt-4 text-center">
                By placing your order, you agree to our{' '}
                <Link href="/terms" className="text-primary hover:underline">Terms of Service</Link>
              </p>
            </div>

            <div className="mt-6 text-sm text-muted-foreground">
              <p className="flex items-center mb-2">
                <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Free cancellation up to 24 hours before
              </p>
              <p className="flex items-center">
                <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Best price guarantee
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
