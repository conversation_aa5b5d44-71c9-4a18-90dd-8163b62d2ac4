import { <PERSON>ada<PERSON> } from 'next';
import { notFound } from 'next/navigation';
import { ArrowLeft, Star, MapPin, Share2, Heart, Calendar, Users, Clock, CheckCircle } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

// Mock data - in a real app, this would come from your database/API
const listings = [
  {
    id: '1',
    title: 'Luxury Beachfront Villa',
    location: 'Malibu, California',
    price: 1200,
    rating: 4.98,
    reviews: 124,
    images: [
      'https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=1471&q=80',
      'https://images.unsplash.com/photo-1580587771525-78b9dba3b914?ixlib=rb-4.0.3&auto=format&fit=crop&w=1074&q=80',
      'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
    ],
    type: 'Stays & Rentals',
    description: 'Experience the ultimate in luxury at our breathtaking beachfront villa in Malibu. This stunning property features panoramic ocean views, a private infinity pool, and direct beach access. The villa includes 5 bedrooms, 6.5 bathrooms, a gourmet kitchen, and a home theater. Perfect for those seeking privacy, comfort, and world-class amenities.',
    amenities: [
      'Private Beach Access', 'Infinity Pool', 'Home Theater', 'Gourmet Kitchen',
      'Ocean View', 'Smart Home System', 'Private Chef Available', 'Daily Housekeeping'
    ],
    host: {
      name: 'Elite Stays',
      joined: '2018',
      verified: true,
      rating: 4.95,
      responseRate: '100%',
      responseTime: 'within an hour'
    },
    availability: 'Calendar available upon booking',
    cancellation: 'Free cancellation up to 30 days before check-in'
  },
  // Add more listings as needed
];

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const listing = listings.find(item => item.id === params.id);
  
  if (!listing) {
    return {
      title: 'Listing Not Found | LXGO Marketplace',
    };
  }

  return {
    title: `${listing.title} | LXGO Marketplace`,
    description: listing.description.substring(0, 160),
    openGraph: {
      images: [listing.images[0]],
    },
  };
}

export default function ListingPage({ params }: { params: { id: string } }) {
  const listing = listings.find(item => item.id === params.id);
  
  if (!listing) {
    notFound();
  }

  return (
    <div className="pt-24 pb-16 md:pt-28">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <Link 
            href="/marketplace" 
            className="inline-flex items-center text-primary hover:underline mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Marketplace
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <h1 className="text-3xl md:text-4xl font-bold tracking-tight mb-2">
              {listing.title}
            </h1>
            
            <div className="flex items-center text-muted-foreground mb-6">
              <div className="flex items-center mr-4">
                <Star className="w-5 h-5 text-amber-400 fill-amber-400 mr-1" />
                <span>{listing.rating} ({listing.reviews} reviews)</span>
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-1" />
                <span>{listing.location}</span>
              </div>
            </div>

            {/* Image Gallery */}
            <div className="grid grid-cols-1 gap-4 mb-8">
              <div className="relative aspect-video rounded-xl overflow-hidden">
                <Image
                  src={listing.images[0]}
                  alt={listing.title}
                  fill
                  className="object-cover"
                  priority
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                {listing.images.slice(1).map((image, index) => (
                  <div key={index} className="relative aspect-square rounded-xl overflow-hidden">
                    <Image
                      src={image}
                      alt={`${listing.title} ${index + 2}`}
                      fill
                      className="object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Description */}
            <div className="prose max-w-none mb-8">
              <h2 className="text-2xl font-semibold mb-4">About this listing</h2>
              <p className="text-muted-foreground">{listing.description}</p>
            </div>

            {/* Amenities */}
            <div className="mb-8">
              <h2 className="text-2xl font-semibold mb-4">Amenities</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {listing.amenities.map((amenity, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-primary mr-2" />
                    <span>{amenity}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Host Info */}
            <div className="border-t border-border pt-8">
              <h2 className="text-2xl font-semibold mb-4">Hosted by {listing.host.name}</h2>
              <div className="flex items-start gap-4">
                <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center text-2xl font-bold">
                  {listing.host.name.charAt(0)}
                </div>
                <div>
                  <p className="font-medium">{listing.host.name} {listing.host.verified && <span className="text-primary text-sm ml-2">✓ Verified</span>}</p>
                  <p className="text-sm text-muted-foreground">Joined in {listing.host.joined}</p>
                  <div className="mt-2 text-sm space-y-1">
                    <p>Response rate: {listing.host.responseRate}</p>
                    <p>Response time: {listing.host.responseTime}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Booking Sidebar */}
          <div className="lg:sticky lg:top-32 self-start">
            <div className="border border-border rounded-xl p-6 shadow-sm">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <p className="text-2xl font-bold">${listing.price} <span className="text-base font-normal text-muted-foreground">/ day</span></p>
                  <div className="flex items-center mt-1">
                    <Star className="w-4 h-4 text-amber-400 fill-amber-400 mr-1" />
                    <span>{listing.rating} · {listing.reviews} reviews</span>
                  </div>
                </div>
                <button className="p-2 rounded-full hover:bg-muted">
                  <Share2 className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-4 mb-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="border border-border rounded-lg p-3">
                    <p className="text-sm text-muted-foreground">Check-in</p>
                    <p className="font-medium">Add dates</p>
                  </div>
                  <div className="border border-border rounded-lg p-3">
                    <p className="text-sm text-muted-foreground">Checkout</p>
                    <p className="font-medium">Add dates</p>
                  </div>
                </div>
                <div className="border border-border rounded-lg p-3">
                  <p className="text-sm text-muted-foreground mb-1">Guests</p>
                  <p className="font-medium">1 guest</p>
                </div>
              </div>

              <button className="w-full bg-primary text-primary-foreground py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors mb-4">
                Check availability
              </button>

              <p className="text-sm text-center text-muted-foreground">
                {listing.cancellation}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
