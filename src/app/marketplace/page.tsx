import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { Star, MapPin, ArrowRight, Search, Filter, Calendar, Users } from 'lucide-react';
import Image from 'next/image';

// Mock data - in a real app, this would come from your database/API
const listings = [
  {
    id: 1,
    title: 'Luxury Beachfront Villa',
    location: 'Malibu, California',
    price: 1200,
    rating: 4.98,
    reviews: 124,
    image: 'https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=1471&q=80',
    type: 'stays-rentals',
    featured: true
  },
  {
    id: 2,
    title: 'Sunset Yacht Charter',
    location: 'Miami, Florida',
    price: 2500,
    rating: 4.9,
    reviews: 89,
    image: 'https://images.unsplash.com/photo-1501785888041-af3ef285b470?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
    type: 'boats-yachts',
    featured: true
  },
  {
    id: 3,
    title: 'Luxury Car Rental',
    location: 'Los Angeles, California',
    price: 350,
    rating: 4.95,
    reviews: 210,
    image: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
    type: 'vehicles',
    featured: true
  },
  {
    id: 4,
    title: 'Private Jet to Aspen',
    location: 'New York to Aspen',
    price: 15000,
    rating: 5.0,
    reviews: 45,
    image: 'https://images.unsplash.com/photo-1436491865333-4b283dc4429e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
    type: 'travel',
    featured: true
  },
  {
    id: 5,
    title: 'Private Chef Experience',
    location: 'Your Location',
    price: 500,
    rating: 4.97,
    reviews: 178,
    image: 'https://images.unsplash.com/photo-1414235077428-338989f6d594?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
    type: 'dining',
    featured: true
  },
  {
    id: 6,
    title: 'Luxury Spa Retreat',
    location: 'Sedona, Arizona',
    price: 1200,
    rating: 4.99,
    reviews: 231,
    image: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
    type: 'wellness',
    featured: true
  },
];

const categories = [
  { name: 'Stays & Rentals', slug: 'stays-rentals', icon: '🏠', count: 1245 },
  { name: 'Boats & Yachts', slug: 'boats-yachts', icon: '🛥️', count: 342 },
  { name: 'Luxury Vehicles', slug: 'vehicles', icon: '🚗', count: 789 },
  { name: 'Private Aviation', slug: 'travel', icon: '✈️', count: 156 },
  { name: 'Gourmet Dining', slug: 'dining', icon: '🍽️', count: 423 },
  { name: 'Wellness & Spa', slug: 'wellness', icon: '🧖', count: 267 },
];

export const metadata: Metadata = {
  title: 'Luxury Marketplace | LXGO Concierge',
  description: 'Discover curated luxury experiences, premium services, and exclusive access to the finest lifestyle offerings worldwide.',
};

export default function MarketplacePage() {
  return (
    <div className="pt-24 pb-16 md:pt-28">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <div className="max-w-4xl mx-auto text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
            Luxury Marketplace
          </h1>
          <p className="text-xl text-muted-foreground mb-8">
            Discover and book premium experiences, services, and products from our curated selection.
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto bg-white rounded-full shadow-lg p-1 flex items-center mb-12">
            <div className="flex-1 px-4 flex items-center">
              <Search className="h-5 w-5 text-muted-foreground mr-2" />
              <input
                type="text"
                placeholder="Search for experiences, destinations, or services..."
                className="w-full py-3 outline-none text-foreground placeholder-muted-foreground"
              />
            </div>
            <button className="bg-primary text-primary-foreground px-6 py-3 rounded-full font-medium hover:bg-primary/90 transition-colors">
              Search
            </button>
          </div>
        </div>

        {/* Categories */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold mb-6">Browse by category</h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-4">
            {categories.map((category) => (
              <Link 
                key={category.slug}
                href={`/marketplace/category/${category.slug}`}
                className="group p-4 border border-border rounded-xl hover:border-primary/30 transition-colors text-center"
              >
                <span className="text-3xl mb-2 inline-block group-hover:scale-110 transition-transform">
                  {category.icon}
                </span>
                <h3 className="font-medium">{category.name}</h3>
                <p className="text-sm text-muted-foreground">{category.count}+ listings</p>
              </Link>
            ))}
          </div>
        </div>

        {/* Featured Listings */}
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold">Featured Listings</h2>
            <Link 
              href="/marketplace/all-listings" 
              className="flex items-center text-primary hover:underline"
            >
              View all <ArrowRight className="ml-2 w-4 h-4" />
            </Link>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {listings.map((item) => (
              <div key={item.id} className="group relative overflow-hidden rounded-2xl bg-card border border-border/20 hover:border-primary/30 transition-all hover:shadow-xl">
                <div className="aspect-video relative overflow-hidden">
                  <Link href={`/marketplace/listing/${item.id}`} className="block h-full w-full">
                    <Image
                      src={item.image}
                      alt={item.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                    <div className="absolute top-4 right-4 bg-background/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium flex items-center">
                      <Star className="w-4 h-4 text-amber-400 fill-amber-400 mr-1" />
                      {item.rating} ({item.reviews})
                    </div>
                  </Link>
                </div>
                <div className="p-6">
                  <div className="flex justify-between items-start mb-2">
                    <Link href={`/marketplace/listing/${item.id}`} className="hover:underline">
                      <h4 className="text-xl font-bold line-clamp-1">{item.title}</h4>
                    </Link>
                    <span className="text-sm text-muted-foreground">${item.price}<span className="text-xs">/day</span></span>
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground mb-4">
                    <MapPin className="w-4 h-4 mr-1" />
                    {item.location}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="inline-block px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium">
                      {categories.find(cat => cat.slug === item.type)?.name || item.type}
                    </span>
                    <Link href={`/marketplace/listing/${item.id}`} className="text-primary hover:underline font-medium">
                      View details
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-2xl p-8 md:p-12 text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-4">Can't find what you're looking for?</h2>
          <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
            Our concierge team can help you find and book exclusive experiences tailored to your preferences.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link 
              href="/marketplace/contact"
              className="bg-primary text-primary-foreground px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors inline-flex items-center justify-center"
            >
              Contact Sales
            </Link>
            <Link 
              href="/auth/login?redirect=/marketplace"
              className="border border-primary text-primary px-6 py-3 rounded-lg font-medium hover:bg-primary/5 transition-colors inline-flex items-center justify-center"
            >
              Get Started
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
