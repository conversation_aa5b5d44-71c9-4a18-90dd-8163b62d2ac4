import { Metadata } from 'next';
import dynamic from 'next/dynamic';

// Using dynamic import to handle client components
const MarketplaceSection = dynamic(
  () => import('@/components/home/<USER>'),
  { ssr: true }
);

export const metadata: Metadata = {
  title: 'Luxury Marketplace | LXGO Concierge',
  description: 'Discover curated luxury experiences, premium services, and exclusive access to the finest lifestyle offerings worldwide.',
};

export default function MarketplacePage() {
  return (
    <div className="pt-24 pb-16 md:pt-28">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-4">
            Luxury Marketplace
          </h1>
          <p className="text-xl text-muted-foreground">
            Discover and book premium experiences, services, and products from our curated selection.
          </p>
        </div>
        
        <MarketplaceSection />
      </div>
    </div>
  );
}
