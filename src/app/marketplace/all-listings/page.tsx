import { <PERSON>ada<PERSON> } from 'next';
import { ArrowLeft, Filter, Search, SlidersHorizontal } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { Star, MapPin } from 'lucide-react';

// Mock data - in a real app, this would come from your database/API
const allListings = [
  {
    id: 1,
    title: 'Luxury Beachfront Villa',
    location: 'Malibu, California',
    price: 1200,
    rating: 4.98,
    reviews: 124,
    image: 'https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=1471&q=80',
    type: 'stays-rentals',
  },
  {
    id: 2,
    title: 'Sunset Yacht Charter',
    location: 'Miami, Florida',
    price: 2500,
    rating: 4.9,
    reviews: 89,
    image: 'https://images.unsplash.com/photo-1501785888041-af3ef285b470?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
    type: 'boats-yachts',
  },
  {
    id: 3,
    title: 'Luxury Car Rental',
    location: 'Los Angeles, California',
    price: 350,
    rating: 4.95,
    reviews: 210,
    image: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
    type: 'vehicles',
  },
  {
    id: 4,
    title: 'Private Jet to Aspen',
    location: 'New York to Aspen',
    price: 15000,
    rating: 5.0,
    reviews: 45,
    image: 'https://images.unsplash.com/photo-1436491865333-4b283dc4429e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
    type: 'travel',
  },
  {
    id: 5,
    title: 'Private Chef Experience',
    location: 'Your Location',
    price: 500,
    rating: 4.97,
    reviews: 178,
    image: 'https://images.unsplash.com/photo-1414235077428-338989f6d594?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
    type: 'dining',
  },
  {
    id: 6,
    title: 'Luxury Spa Retreat',
    location: 'Sedona, Arizona',
    price: 1200,
    rating: 4.99,
    reviews: 231,
    image: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
    type: 'wellness',
  },
  // Add more listings as needed
];

const categories = [
  { name: 'All', slug: 'all', count: allListings.length },
  { name: 'Stays & Rentals', slug: 'stays-rentals', count: 1245 },
  { name: 'Boats & Yachts', slug: 'boats-yachts', count: 342 },
  { name: 'Luxury Vehicles', slug: 'vehicles', count: 789 },
  { name: 'Private Aviation', slug: 'travel', count: 156 },
  { name: 'Gourmet Dining', slug: 'dining', count: 423 },
  { name: 'Wellness & Spa', slug: 'wellness', count: 267 },
];

export const metadata: Metadata = {
  title: 'All Listings | LXGO Marketplace',
  description: 'Browse all luxury listings and experiences',
};

export default function AllListingsPage() {
  return (
    <div className="pt-24 pb-16 md:pt-28">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button and Title */}
        <div className="mb-8">
          <Link 
            href="/marketplace" 
            className="inline-flex items-center text-primary hover:underline mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Marketplace
          </Link>
          
          <h1 className="text-3xl md:text-4xl font-bold tracking-tight mb-2">
            All Listings
          </h1>
          <p className="text-muted-foreground">
            Discover all our luxury offerings from around the world
          </p>
        </div>

        {/* Search and Filter Bar */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search listings..."
                className="w-full pl-10 pr-4 py-3 rounded-lg border border-border focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            <button className="px-4 py-3 rounded-lg border border-border hover:bg-muted/50 flex items-center justify-center gap-2">
              <SlidersHorizontal className="w-5 h-5" />
              <span>Filters</span>
            </button>
          </div>
        </div>

        {/* Category Tabs */}
        <div className="mb-8 overflow-x-auto">
          <div className="flex space-x-2 pb-2">
            {categories.map((category) => (
              <button
                key={category.slug}
                className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap ${
                  category.slug === 'all'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted/50 hover:bg-muted'
                }`}
              >
                {category.name}
                <span className="ml-1.5 text-xs bg-black/10 dark:bg-white/20 px-2 py-0.5 rounded-full">
                  {category.count}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Listings Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {allListings.map((listing) => (
            <div key={listing.id} className="group relative overflow-hidden rounded-2xl bg-card border border-border/20 hover:border-primary/30 transition-all hover:shadow-xl">
              <Link href={`/marketplace/listing/${listing.id}`}>
                <div className="aspect-square relative overflow-hidden">
                  <Image
                    src={listing.image}
                    alt={listing.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute top-3 right-3 bg-background/90 backdrop-blur-sm px-2 py-1 rounded-full text-sm font-medium flex items-center">
                    <Star className="w-4 h-4 text-amber-400 fill-amber-400 mr-1" />
                    {listing.rating} ({listing.reviews})
                  </div>
                </div>
                <div className="p-4">
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="font-semibold text-lg line-clamp-1">{listing.title}</h3>
                    <p className="text-lg font-medium">${listing.price}<span className="text-sm text-muted-foreground">/day</span></p>
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground mb-2">
                    <MapPin className="w-4 h-4 mr-1" />
                    {listing.location}
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="inline-block px-2.5 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium">
                      {categories.find(cat => cat.slug === listing.type)?.name || listing.type}
                    </span>
                    <span className="text-sm text-muted-foreground">View details</span>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>

        {/* Pagination */}
        <div className="mt-12 flex justify-center">
          <nav className="flex items-center gap-1">
            <button className="px-3 py-1.5 rounded-lg border border-border hover:bg-muted">
              Previous
            </button>
            <button className="w-10 h-10 rounded-lg bg-primary text-primary-foreground font-medium">
              1
            </button>
            <button className="w-10 h-10 rounded-lg hover:bg-muted">2</button>
            <button className="w-10 h-10 rounded-lg hover:bg-muted">3</button>
            <span className="px-2">...</span>
            <button className="w-10 h-10 rounded-lg hover:bg-muted">8</button>
            <button className="px-3 py-1.5 rounded-lg border border-border hover:bg-muted">
              Next
            </button>
          </nav>
        </div>
      </div>
    </div>
  );
}
