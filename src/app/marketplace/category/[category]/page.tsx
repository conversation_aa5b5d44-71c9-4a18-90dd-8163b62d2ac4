import { <PERSON>ada<PERSON> } from 'next';
import { notFound } from 'next/navigation';
import { ArrowLeft, Star, MapPin, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

// Mock data - in a real app, this would come from your database/API
const categories = [
  { slug: 'stays-rentals', name: 'Stays & Rentals', description: 'Luxury villas, penthouses, and exclusive residences' },
  { slug: 'boats-yachts', name: 'Boats & Yachts', description: 'Private yacht charters and boating experiences' },
  { slug: 'vehicles', name: 'Luxury Vehicles', description: 'High-end car rentals and chauffeur services' },
  { slug: 'events', name: 'Exclusive Events', description: 'VIP access to the most sought-after events' },
  { slug: 'dining', name: 'Gourmet Dining', description: 'Michelin-starred restaurants and private chefs' },
  { slug: 'wellness', name: 'Wellness & Spa', description: 'Premium spa treatments and wellness retreats' },
];

// Mock listings data
const allListings = [
  {
    id: 1,
    title: 'Luxury Beachfront Villa',
    location: 'Malibu, California',
    price: 1200,
    rating: 4.98,
    reviews: 124,
    image: 'https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=1471&q=80',
    type: 'stays-rentals',
    featured: true
  },
  {
    id: 2,
    title: 'Sunset Yacht Charter',
    location: 'Miami, Florida',
    price: 2500,
    rating: 4.9,
    reviews: 89,
    image: 'https://images.unsplash.com/photo-1501785888041-af3ef285b470?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
    type: 'boats-yachts',
    featured: true
  },
  {
    id: 3,
    title: 'Luxury Car Rental',
    location: 'Los Angeles, California',
    price: 350,
    rating: 4.95,
    reviews: 210,
    image: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
    type: 'vehicles',
    featured: true
  },
  // Add more listings as needed
];

export async function generateMetadata({ params }: { params: { category: string } }): Promise<Metadata> {
  const category = categories.find(cat => cat.slug === params.category);
  
  if (!category) {
    return {
      title: 'Category Not Found | LXGO Marketplace',
    };
  }

  return {
    title: `${category.name} | LXGO Marketplace`,
    description: category.description,
  };
}

export default function CategoryPage({ params }: { params: { category: string } }) {
  const category = categories.find(cat => cat.slug === params.category);
  
  if (!category) {
    notFound();
  }

  const categoryListings = allListings.filter(listing => listing.type === params.category);

  return (
    <div className="pt-24 pb-16 md:pt-28">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <Link 
            href="/marketplace" 
            className="inline-flex items-center text-primary hover:underline mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Marketplace
          </Link>
          
          <h1 className="text-3xl md:text-4xl font-bold tracking-tight mb-2">
            {category.name}
          </h1>
          <p className="text-muted-foreground">
            {category.description}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {categoryListings.length > 0 ? (
            categoryListings.map((item) => (
              <div key={item.id} className="group relative overflow-hidden rounded-2xl bg-card border border-border/20 hover:border-primary/30 transition-all hover:shadow-xl">
                <div className="aspect-video relative overflow-hidden">
                  <Image
                    src={item.image}
                    alt={item.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute top-4 right-4 bg-background/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium flex items-center">
                    <Star className="w-4 h-4 text-amber-400 fill-amber-400 mr-1" />
                    {item.rating} ({item.reviews})
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="text-xl font-bold line-clamp-1">{item.title}</h4>
                    <span className="text-sm text-muted-foreground">${item.price}<span className="text-xs">/day</span></span>
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground mb-4">
                    <MapPin className="w-4 h-4 mr-1" />
                    {item.location}
                  </div>
                  <Link 
                    href={`/marketplace/listing/${item.id}`}
                    className="inline-flex items-center text-primary hover:underline text-sm font-medium"
                  >
                    View details <ArrowRight className="ml-2 w-4 h-4" />
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <p className="text-muted-foreground">No listings found in this category.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
