'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import {
  Clock,
  DollarSign,
  AlertCircle,
  CheckCircle,
  XCircle,
  Phone,
  Mail,
  FileText,
  RefreshCw,
  Shield
} from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

const cancellationPolicies = [
  {
    service: 'Private Aviation',
    icon: '✈️',
    policies: [
      { timeframe: '48+ hours', refund: '100%', fee: 'None' },
      { timeframe: '24-48 hours', refund: '75%', fee: '$500' },
      { timeframe: '12-24 hours', refund: '50%', fee: '$1,000' },
      { timeframe: 'Less than 12 hours', refund: '0%', fee: 'Full charge' }
    ]
  },
  {
    service: 'Luxury Accommodations',
    icon: '🏨',
    policies: [
      { timeframe: '72+ hours', refund: '100%', fee: 'None' },
      { timeframe: '48-72 hours', refund: '85%', fee: '$200' },
      { timeframe: '24-48 hours', refund: '60%', fee: '$400' },
      { timeframe: 'Less than 24 hours', refund: '0%', fee: 'Full charge' }
    ]
  },
  {
    service: 'Fine Dining',
    icon: '🍽️',
    policies: [
      { timeframe: '24+ hours', refund: '100%', fee: 'None' },
      { timeframe: '12-24 hours', refund: '75%', fee: '$100' },
      { timeframe: '6-12 hours', refund: '50%', fee: '$150' },
      { timeframe: 'Less than 6 hours', refund: '0%', fee: 'Full charge' }
    ]
  },
  {
    service: 'Event Planning',
    icon: '🎉',
    policies: [
      { timeframe: '30+ days', refund: '90%', fee: '10% of total' },
      { timeframe: '14-30 days', refund: '70%', fee: '30% of total' },
      { timeframe: '7-14 days', refund: '50%', fee: '50% of total' },
      { timeframe: 'Less than 7 days', refund: '0%', fee: 'Full charge' }
    ]
  }
];

const cancellationSteps = [
  {
    step: 1,
    title: 'Review Your Booking',
    description: 'Check your booking details and cancellation deadline in your account.',
    icon: FileText
  },
  {
    step: 2,
    title: 'Contact Concierge',
    description: 'Reach out to our concierge team via phone, email, or chat.',
    icon: Phone
  },
  {
    step: 3,
    title: 'Confirm Cancellation',
    description: 'Receive confirmation and refund details within 24 hours.',
    icon: CheckCircle
  },
  {
    step: 4,
    title: 'Process Refund',
    description: 'Refunds are processed within 5-7 business days to original payment method.',
    icon: RefreshCw
  }
];

const specialCircumstances = [
  {
    title: 'Medical Emergencies',
    description: 'Full refund available with valid medical documentation.',
    icon: Shield,
    color: 'text-green-500'
  },
  {
    title: 'Natural Disasters',
    description: 'No cancellation fees for government-declared emergencies.',
    icon: AlertCircle,
    color: 'text-blue-500'
  },
  {
    title: 'Travel Restrictions',
    description: 'Flexible policies for COVID-19 or other travel restrictions.',
    icon: XCircle,
    color: 'text-orange-500'
  }
];

export default function CancellationPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-transparent to-secondary/5" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,oklch(0.55_0.18_180_/_0.08),transparent_50%)]" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="max-w-4xl mx-auto text-center"
            >
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-serif font-bold mb-6">
                <span className="block">Cancellation</span>
                <span className="text-luxury">Policy</span>
              </h1>
              <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12">
                Flexible cancellation options designed to provide peace of mind 
                for your luxury bookings.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Quick Contact */}
        <section className="py-10 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="glass rounded-2xl p-6 border border-primary/20 mb-16"
              >
                <div className="flex flex-col md:flex-row items-center justify-between gap-6">
                  <div>
                    <h2 className="text-xl font-serif font-bold mb-2">Need to Cancel?</h2>
                    <p className="text-muted-foreground">Contact our concierge team for immediate assistance.</p>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <button className="px-6 py-3 rounded-full gradient-luxury text-white font-medium hover:scale-105 transition-transform flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      Call Now
                    </button>
                    <button className="px-6 py-3 rounded-full glass border border-primary/20 text-foreground font-medium hover:border-primary/40 transition-colors flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      Email Us
                    </button>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Cancellation Policies */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Service <span className="text-luxury">Policies</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Cancellation terms vary by service type and timing. Review the specific 
                policies for your booking below.
              </p>
            </motion.div>

            <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
              {cancellationPolicies.map((service, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-8 border border-border/20 hover:border-primary/30 transition-all"
                >
                  <div className="flex items-center gap-3 mb-6">
                    <span className="text-3xl">{service.icon}</span>
                    <h3 className="text-2xl font-serif font-bold">{service.service}</h3>
                  </div>
                  
                  <div className="space-y-4">
                    {service.policies.map((policy, policyIndex) => (
                      <div key={policyIndex} className="flex items-center justify-between p-4 rounded-xl glass border border-border/10">
                        <div className="flex items-center gap-3">
                          <Clock className="w-5 h-5 text-primary" />
                          <span className="font-medium">{policy.timeframe}</span>
                        </div>
                        <div className="flex items-center gap-4 text-sm">
                          <div className="flex items-center gap-1">
                            <DollarSign className="w-4 h-4 text-green-500" />
                            <span className="text-green-600 font-medium">{policy.refund}</span>
                          </div>
                          <div className="text-muted-foreground">
                            Fee: {policy.fee}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Cancellation Process */}
        <section className="py-20 relative">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent" />
          </div>
          
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                How to <span className="text-luxury">Cancel</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Simple steps to cancel your booking and process your refund.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
              {cancellationSteps.map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="w-20 h-20 mx-auto mb-6 rounded-full gradient-luxury flex items-center justify-center text-white text-2xl font-bold">
                    {step.step}
                  </div>
                  <div className="w-12 h-12 mx-auto mb-4 rounded-full glass border border-primary/20 flex items-center justify-center">
                    <step.icon className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="text-lg font-serif font-bold mb-3">{step.title}</h3>
                  <p className="text-muted-foreground text-sm">{step.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Special Circumstances */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Special <span className="text-luxury">Circumstances</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                We understand that unexpected situations arise. Special considerations 
                apply for certain circumstances.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              {specialCircumstances.map((circumstance, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="glass rounded-2xl p-6 border border-border/20 text-center"
                >
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full glass border border-border/20 flex items-center justify-center ${circumstance.color}`}>
                    <circumstance.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-lg font-serif font-bold mb-3">{circumstance.title}</h3>
                  <p className="text-muted-foreground text-sm">{circumstance.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Important Notes */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="glass rounded-2xl p-8 border border-amber-500/20 bg-amber-50/10"
              >
                <div className="flex items-start gap-4">
                  <AlertCircle className="w-6 h-6 text-amber-500 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="text-xl font-serif font-bold mb-4">Important Notes</h3>
                    <ul className="space-y-3 text-muted-foreground">
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 rounded-full bg-amber-500 flex-shrink-0 mt-2"></span>
                        <span>Cancellation times are based on the local time zone of your service location.</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 rounded-full bg-amber-500 flex-shrink-0 mt-2"></span>
                        <span>Refunds are processed to the original payment method within 5-7 business days.</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 rounded-full bg-amber-500 flex-shrink-0 mt-2"></span>
                        <span>Some partner services may have different cancellation terms, which will be clearly stated at booking.</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1.5 h-1.5 rounded-full bg-amber-500 flex-shrink-0 mt-2"></span>
                        <span>Group bookings and custom experiences may have unique cancellation policies.</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-20 relative">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 gradient-luxury opacity-10" />
          </div>
          
          <div className="container mx-auto px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="max-w-3xl mx-auto"
            >
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-6">
                Need <span className="text-luxury">Help</span>?
              </h2>
              <p className="text-xl text-muted-foreground mb-8">
                Our concierge team is available 24/7 to assist with cancellations 
                and answer any questions about our policies.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/contact"
                  className="px-8 py-4 rounded-full gradient-luxury text-white font-semibold hover:scale-105 transition-transform shadow-lg"
                >
                  Contact Concierge
                </Link>
                <Link
                  href="/help"
                  className="px-8 py-4 rounded-full glass border border-primary/20 text-foreground font-semibold hover:border-primary/40 transition-colors"
                >
                  View FAQ
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
